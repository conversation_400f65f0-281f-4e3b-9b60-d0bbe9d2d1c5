const express = require('express');
const path = require('path');
const chartRoutes = require('./routes/chartRoutes');
const dashboardRoutes = require('./routes/dashboardRoutes');
const { startScheduler, generateInitialChart } = require('./schedulers/chartScheduler');

// Create Express application
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static file serving for output directory (optional - to serve generated charts)
app.use('/static', express.static(path.join(__dirname, 'output')));

// Routes
app.use('/', chartRoutes);
app.use('/', dashboardRoutes);

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Backend Charting Service',
        version: '1.0.0',
        endpoints: {
            'GET /': 'This help message',
            'GET /generate-chart': 'Generate a new revenue chart',
            'GET /chart-image': 'Serve the generated chart image',
            'GET /generate-pdf-report': 'Generate PDF report from chartData.json',
            'GET /pdf-report': 'Serve the generated PDF report',
            'GET /static/chart.png': 'Direct access to chart image (if exists)',
            'GET /anl/api/dashboard/:id': 'Get dashboard data by ID (requires Bearer token)',
            'GET /anl/api/dashboard/:id/widgets': 'Get dashboard widgets (requires Bearer token)',
            'POST /anl/api/dashboard/:id/refresh': 'Refresh dashboard data (requires Bearer token)'
        },
        features: [
            'Automated chart generation every 24 hours at midnight',
            'On-demand chart generation via API',
            'Chart.js powered visualizations',
            'PNG image output'
        ],
        timestamp: new Date().toISOString()
    });
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        memory: process.memoryUsage()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found',
        availableEndpoints: [
            'GET /',
            'GET /generate-chart',
            'GET /chart-image',
            'GET /health'
        ]
    });
});

// Initialize server
async function startServer() {
    try {
        console.log('Starting Backend Charting Service...');

        // Generate initial chart
        console.log('Generating initial chart...');
        await generateInitialChart();

        // Start the cron scheduler
        console.log('Starting chart scheduler...');
        startScheduler();

        // Start the Express server
        app.listen(PORT, () => {
            console.log(`\n🚀 Server is running on port ${PORT}`);
            console.log(`📊 Chart generation service is active`);
            console.log(`⏰ Scheduled chart generation: Every day at midnight`);
            console.log(`\nAvailable endpoints:`);
            console.log(`  • GET http://localhost:${PORT}/`);
            console.log(`  • GET http://localhost:${PORT}/generate-chart`);
            console.log(`  • GET http://localhost:${PORT}/chart-image`);
            console.log(`  • GET http://localhost:${PORT}/generate-pdf-report`);
            console.log(`  • GET http://localhost:${PORT}/pdf-report`);
            console.log(`  • GET http://localhost:${PORT}/health`);
            console.log(`  • GET http://localhost:${PORT}/static/chart.png`);
            console.log(`  • GET http://localhost:${PORT}/dashboard/:id`);
            console.log(`  • GET http://localhost:${PORT}/dashboard/:id/widgets`);
            console.log(`  • POST http://localhost:${PORT}/dashboard/:id/refresh`);

        });

    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT. Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM. Shutting down gracefully...');
    process.exit(0);
});

// Start the server
startServer();
