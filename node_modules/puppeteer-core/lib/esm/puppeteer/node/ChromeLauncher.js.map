{"version": 3, "file": "ChromeLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ChromeLauncher.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAE7B,OAAO,EACL,2BAA2B,EAC3B,OAAO,IAAI,iBAAiB,EAC5B,oBAAoB,IAAI,4BAA4B,GACrD,MAAM,qBAAqB,CAAC;AAG7B,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAC,eAAe,EAA0B,MAAM,sBAAsB,CAAC;AAG9E,OAAO,EAAC,EAAE,EAAC,MAAM,cAAc,CAAC;AAEhC;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,eAAe;IACjD,YAAY,SAAwB;QAClC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEQ,MAAM,CAAC,UAAyB,EAAE;QACzC,IACE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,KAAK,MAAM;YAChD,OAAO,CAAC,QAAQ,KAAK,QAAQ;YAC7B,OAAO,CAAC,IAAI,KAAK,KAAK,EACtB,CAAC;YACD,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CACV;oBACE,yBAAyB;oBACzB,8CAA8C;oBAC9C,kFAAkF;oBAClF,oFAAoF;oBACpF,iFAAiF;oBACjF,oCAAoC;iBACrC,CAAC,IAAI,CAAC,MAAM,CAAC,CACf,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,sBAAsB,CACnC,UAAyB,EAAE;QAE3B,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,IAAI,GAAG,KAAK,EACZ,aAAa,EACb,OAAO,EACP,cAAc,GACf,GAAG,OAAO,CAAC;QAEZ,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACrD,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAClB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAChC,CAAC;QAED,IACE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/B,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,CACJ,CAAC,aAAa,EACd,2EAA2E,CAC5E,CAAC;gBACF,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAE9B,sEAAsE;QACtE,gEAAgE;QAChE,IAAI,gBAAgB,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACrD,OAAO,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACzB,iBAAiB,GAAG,IAAI,CAAC;YACzB,eAAe,CAAC,IAAI,CAClB,mBAAmB,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,CAC1D,CAAC;YACF,gBAAgB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,WAAW,GAAG,eAAe,CAAC,gBAAgB,CAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,MAAM,CAAC,OAAO,WAAW,KAAK,QAAQ,EAAE,gCAAgC,CAAC,CAAC;QAE1E,IAAI,gBAAgB,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,CACJ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAC3C,+EAA+E,CAChF,CAAC;YACF,gBAAgB,GAAG,OAAO;gBACxB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9B,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO;YACL,cAAc,EAAE,gBAAgB;YAChC,IAAI,EAAE,eAAe;YACrB,iBAAiB;YACjB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,gBAAgB,CAC7B,IAAY,EACZ,IAAuB;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;YACjB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAEQ,WAAW,CAAC,UAAyB,EAAE;QAC9C,+FAA+F;QAE/F,MAAM,oBAAoB,GAAG,WAAW,CACtC,oBAAoB,EACpB,OAAO,CAAC,IAAI,CACb,CAAC;QACF,IAAI,OAAO,CAAC,IAAI,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,oCAAoC,GACxC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,KAAK,MAAM,CAAC;QAExE,mEAAmE;QACnE,MAAM,gBAAgB,GAAG;YACvB,WAAW;YACX,uDAAuD;YACvD,eAAe;YACf,aAAa;YACb,mBAAmB;YACnB,GAAG,CAAC,oCAAoC;gBACtC,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC;oBACE,4BAA4B;oBAC5B,sCAAsC;oBACtC,sDAAsD;oBACtD,yBAAyB;iBAC1B,CAAC;YACN,GAAG,oBAAoB;SACxB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACjB,OAAO,OAAO,KAAK,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,OAAO,CAAC,IAAI,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QACzD,CAAC;QAED,kEAAkE;QAClE,MAAM,eAAe,GAAG;YACtB,UAAU;YACV,0CAA0C;YAC1C,GAAG,mBAAmB;SACvB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACjB,OAAO,OAAO,KAAK,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG;YACtB,0BAA0B;YAC1B,iCAAiC;YACjC,uCAAuC;YACvC,0CAA0C;YAC1C,oBAAoB;YACpB,0CAA0C;YAC1C,sDAAsD;YACtD,0BAA0B,EAAE,6BAA6B;YACzD,wBAAwB;YACxB,yBAAyB;YACzB,wBAAwB;YACxB,oBAAoB;YACpB,mCAAmC;YACnC,0BAA0B;YAC1B,4BAA4B;YAC5B,kCAAkC;YAClC,uCAAuC;YACvC,gBAAgB;YAChB,qBAAqB;YACrB,qBAAqB;YACrB,4BAA4B;YAC5B,iCAAiC;YACjC,0BAA0B;YAC1B,gBAAgB;YAChB,wBAAwB;YACxB,qBAAqB;YACrB,sBAAsB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClD,qBAAqB,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;SACjD,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACb,OAAO,GAAG,KAAK,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,EACX,gBAAgB,GAAG,KAAK,GACzB,GAAG,OAAO,CAAC;QACZ,IAAI,WAAW,EAAE,CAAC;YAChB,eAAe,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,eAAe,CAAC,IAAI,CAClB,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,gBAAgB,EACtD,mBAAmB,EACnB,cAAc,CACf,CAAC;QACJ,CAAC;QACD,eAAe,CAAC,IAAI,CAClB,gBAAgB;YACd,CAAC,CAAC,qCAAqC;YACvC,CAAC,CAAC,sBAAsB,CAC3B,CAAC;QACF,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF,CAAC;YACD,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC;QACD,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9B,OAAO,eAAe,CAAC;IACzB,CAAC;IAEQ,cAAc,CACrB,OAA8B,EAC9B,YAAY,GAAG,IAAI;QAEnB,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,2BAA2B,CAAC;gBACjC,OAAO,EAAE,iBAAiB,CAAC,MAAM;gBACjC,OAAO,EAAE,wCAAwC,CAAC,OAAO,CAAC;aAC3D,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF;AAED,SAAS,wCAAwC,CAC/C,OAA6B;IAE7B,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,QAAQ;YACX,OAAO,4BAA4B,CAAC,MAAM,CAAC;QAC7C,KAAK,YAAY;YACf,OAAO,4BAA4B,CAAC,GAAG,CAAC;QAC1C,KAAK,aAAa;YAChB,OAAO,4BAA4B,CAAC,IAAI,CAAC;QAC3C,KAAK,eAAe;YAClB,OAAO,4BAA4B,CAAC,MAAM,CAAC;IAC/C,CAAC;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY,EAAE,UAAoB,EAAE;IAC9D,OAAO,OAAO;SACX,MAAM,CAAC,CAAC,CAAC,EAAE;QACV,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;IAC9D,CAAC,CAAC;SACD,GAAG,CAAC,CAAC,CAAC,EAAE;QACP,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;IACxD,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,CAAC,EAAE;QACV,OAAO,CAAC,CAAC;IACX,CAAC,CAAa,CAAC;AACnB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,KAAe,EAAE,IAAY;IAC/D,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE,CAAC;YAC1B,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,CAAC,EAAE,CAAC;QACN,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}