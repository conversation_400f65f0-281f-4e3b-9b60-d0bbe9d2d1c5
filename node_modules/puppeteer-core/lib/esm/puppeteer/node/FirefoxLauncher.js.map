{"version": 3, "file": "FirefoxLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/FirefoxLauncher.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAC,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAE7B,OAAO,EAAC,OAAO,IAAI,iBAAiB,EAAE,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAEhF,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAC,eAAe,EAA0B,MAAM,sBAAsB,CAAC;AAG9E,OAAO,EAAC,EAAE,EAAC,MAAM,cAAc,CAAC;AAEhC;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,eAAe;IAClD,YAAY,SAAwB;QAClC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,cAAc,CACnB,iBAA2C;QAE3C,OAAO;YACL,GAAG,iBAAiB;YACpB,sEAAsE;YACtE,sEAAsE;YACtE,qEAAqE;YACrE,qCAAqC,EAAE,CAAC;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,sBAAsB,CACnC,UAAyB,EAAE;QAE3B,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,cAAc,EACd,IAAI,GAAG,KAAK,EACZ,iBAAiB,GAAG,EAAE,EACtB,aAAa,GAAG,IAAI,GACrB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC5C,gBAAgB,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,IACE,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,CACJ,aAAa,KAAK,IAAI,EACtB,2EAA2E,CAC5E,CAAC;YACJ,CAAC;YACD,gBAAgB,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,WAA+B,CAAC;QACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAE7B,gEAAgE;QAChE,gEAAgE;QAChE,MAAM,eAAe,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvD,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3B,WAAW,GAAG,gBAAgB,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;YAED,+DAA+D;YAC/D,6BAA6B;YAC7B,iBAAiB,GAAG,KAAK,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACnD,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,aAAa,CAAC,iBAAiB,CAAC,OAAO,EAAE;YAC7C,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,eAAe,CAAC,cAAc,CAAC,iBAAiB,CAAC;SAC/D,CAAC,CAAC;QAEH,IAAI,iBAAyB,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,cAAc,EAAE,CAAC;YACtD,MAAM,CACJ,cAAc,EACd,gEAAgE,CACjE,CAAC;YACF,iBAAiB,GAAG,cAAc,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACrD,CAAC;QAED,OAAO;YACL,iBAAiB;YACjB,WAAW;YACX,IAAI,EAAE,gBAAgB;YACtB,cAAc,EAAE,iBAAiB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,gBAAgB,CAC7B,WAAmB,EACnB,IAAuB;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,YAAY,CAAC;gBAClC,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;gBAE5C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CACtC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAC,IAAI,EAAC,EAAE;oBAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,YAAY,CAAC,CAAC;oBACpE,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;wBACnC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;wBAC/C,MAAM,MAAM,CAAC,SAAS,CAAC,CAAC;wBACxB,MAAM,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC,CAAC,CACH,CAAC;gBACF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC7B,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;wBACjC,MAAM,MAAM,CAAC,MAAM,CAAC;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAEQ,cAAc,CAAC,CAAU,EAAE,YAAY,GAAG,IAAI;QACrD,OAAO,IAAI,CAAC,qBAAqB,CAC/B,SAAS;QACT,kBAAkB,CAAC,YAAY,CAChC,CAAC;IACJ,CAAC;IAEQ,WAAW,CAAC,UAAyB,EAAE;QAC9C,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GAAG,IAAI,GACnB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,KAAK,QAAQ;gBACX,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,OAAO;gBACV,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC5C,MAAM;QACV,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YAChB,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF,CAAC;YACD,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC;QACD,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/B,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF"}