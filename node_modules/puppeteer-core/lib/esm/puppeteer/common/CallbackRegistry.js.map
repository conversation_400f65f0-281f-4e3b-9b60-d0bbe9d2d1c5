{"version": 3, "file": "CallbackRegistry.js", "sourceRoot": "", "sources": ["../../../../src/common/CallbackRegistry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAC,YAAY,EAAC,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAC,4BAA4B,EAAC,MAAM,qCAAqC,CAAC;AAEjF,OAAO,EAAC,aAAa,EAAE,gBAAgB,EAAC,MAAM,aAAa,CAAC;AAC5D,OAAO,EAAC,UAAU,EAAC,MAAM,WAAW,CAAC;AAErC,MAAM,WAAW,GAAG,4BAA4B,EAAE,CAAC;AAEnD;;;;GAIG;AACH,MAAM,OAAO,gBAAgB;IAC3B,UAAU,GAAG,IAAI,GAAG,EAAoB,CAAC;IACzC,YAAY,GAAG,WAAW,CAAC;IAE3B,MAAM,CACJ,KAAa,EACb,OAA2B,EAC3B,OAA6B;QAE7B,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC;YACH,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sEAAsE;YACtE,YAAY;YACZ,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,MAAM,CAAC,KAAc,CAAC,CAAC;YAChC,MAAM,KAAK,CAAC;QACd,CAAC;QACD,0CAA0C;QAC1C,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,EAAU,EAAE,OAAe,EAAE,eAAwB;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;IACnD,CAAC;IAED,SAAS,CAAC,EAAU,EAAE,KAAa;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,QAAQ,CAAC,MAAM,CAAC,KAAY,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CACL,QAAkB,EAClB,YAAoC,EACpC,eAAwB;QAExB,IAAI,KAAoB,CAAC;QACzB,IAAI,OAAe,CAAC;QACpB,IAAI,YAAY,YAAY,aAAa,EAAE,CAAC;YAC1C,KAAK,GAAG,YAAY,CAAC;YACrB,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC7B,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YACvB,OAAO,GAAG,YAAY,CAAC;QACzB,CAAC;QAED,QAAQ,CAAC,MAAM,CACb,YAAY,CACV,KAAK,EACL,mBAAmB,QAAQ,CAAC,KAAK,MAAM,OAAO,EAAE,EAChD,eAAe,CAChB,CACF,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,EAAU,EAAE,KAAc;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK;QACH,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,yDAAyD;YACzD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CACT,IAAI,KAAK,CACP,GAAG,QAAQ,CAAC,KAAK,sBAAsB,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAC9D,CACF,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AACD;;GAEG;AAEH,MAAM,OAAO,QAAQ;IACnB,GAAG,CAAS;IACZ,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;IAC7B,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAW,CAAC;IACvC,MAAM,CAAiC;IACvC,MAAM,CAAS;IAEf,YAAY,EAAU,EAAE,KAAa,EAAE,OAAgB;QACrD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CACnB,YAAY,CACV,IAAI,CAAC,MAAM,EACX,GAAG,KAAK,4GAA4G,CACrH,CACF,CAAC;YACJ,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,CAAC,KAAc;QACpB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,KAAY;QACjB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF"}