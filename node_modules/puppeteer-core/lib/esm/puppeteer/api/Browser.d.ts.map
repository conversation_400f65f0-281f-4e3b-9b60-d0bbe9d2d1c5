{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/Browser.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAErD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAQhD,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAC9D,OAAO,KAAK,EAAC,MAAM,EAAE,UAAU,EAAC,MAAM,qBAAqB,CAAC;AAC5D,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,+BAA+B,CAAC;AACpE,OAAO,EAAC,YAAY,EAAE,KAAK,SAAS,EAAC,MAAM,2BAA2B,CAAC;AAQvE,OAAO,EAAC,kBAAkB,EAAE,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAExE,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACxD,OAAO,KAAK,EAAC,IAAI,EAAC,MAAM,WAAW,CAAC;AACpC,OAAO,KAAK,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AACxC;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;CACrC;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC;AAE/D;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC;AAE/D;;GAEG;AACH,eAAO,MAAM,qCAAqC,kDAwBhD,CAAC;AAEH;;GAEG;AACH,MAAM,MAAM,UAAU,GAClB,eAAe,GACf,sBAAsB,GACtB,iBAAiB,GACjB,QAAQ,GACR,gBAAgB,GAChB,2BAA2B,GAC3B,iBAAiB,GACjB,aAAa,GACb,WAAW,GACX,gBAAgB,GAChB,eAAe,GACf,cAAc,GACd,YAAY,GACZ,YAAY,GACZ,MAAM,GACN,eAAe,GACf,iBAAiB,GACjB,oBAAoB,GACpB,cAAc,CAAC;AAEnB;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB;AAED;;;;GAIG;AACH,0BAAkB,YAAY;IAC5B;;;;;;OAMG;IACH,YAAY,iBAAiB;IAC7B;;;;;;OAMG;IACH,aAAa,kBAAkB;IAC/B;;;;;;;;;OASG;IACH,aAAa,kBAAkB;IAC/B;;;;;;OAMG;IACH,eAAe,oBAAoB;IACnC;;OAEG;IACH,gBAAgB,qBAAqB;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,aAAc,SAAQ,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;IAC/D,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC;IACvC,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC;IACrC,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,MAAM,CAAC;IACvC,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC;IACrC;;OAEG;IACH,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;CAC7D;AAED;;;GAGG;AACH,MAAM,WAAW,SAAS;IACxB,qBAAqB,EAAE,KAAK,EAAE,CAAC;CAChC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,8BAAsB,OAAQ,SAAQ,YAAY,CAAC,aAAa,CAAC;IAC/D;;OAEG;;IAKH;;;;;;OAMG;IACH,QAAQ,CAAC,OAAO,IAAI,YAAY,GAAG,IAAI;IAEvC;;;;;;;;;;;;;;;;;;OAkBG;IACH,QAAQ,CAAC,oBAAoB,CAC3B,OAAO,CAAC,EAAE,qBAAqB,GAC9B,OAAO,CAAC,cAAc,CAAC;IAE1B;;;;;OAKG;IACH,QAAQ,CAAC,eAAe,IAAI,cAAc,EAAE;IAE5C;;;;;OAKG;IACH,QAAQ,CAAC,qBAAqB,IAAI,cAAc;IAEhD;;;;;;;;;;;;OAYG;IACH,QAAQ,CAAC,UAAU,IAAI,MAAM;IAE7B;;;OAGG;IACH,QAAQ,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAEjC;;;;;;OAMG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,EAAE;IAE5B;;;OAGG;IACH,QAAQ,CAAC,MAAM,IAAI,MAAM;IAEzB;;;;;;;;;;;;;;OAcG;IACG,aAAa,CACjB,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,GAAE,oBAAyB,GACjC,OAAO,CAAC,MAAM,CAAC;IAclB;;;;;;;;;OASG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAY9B;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAEnC;;;;;;OAMG;IACH,QAAQ,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAErC;;;OAGG;IACH,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAE/B;;;OAGG;IACH,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAEpC;;;;;;;OAOG;IACG,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAIlC;;;;;;;OAOG;IACG,SAAS,CAAC,GAAG,OAAO,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAIxD;;;;;;;OAOG;IACG,YAAY,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAIvD;;;;OAIG;IACH,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAExD;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAEtD;;;;OAIG;IACH,WAAW,IAAI,OAAO;IAItB;;OAEG;IACH,QAAQ,KAAK,SAAS,IAAI,OAAO,CAAC;IAElC,gBAAgB;IACP,CAAC,aAAa,CAAC,IAAI,IAAI;IAOhC,gBAAgB;IAChB,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;IAOrC;;OAEG;IACH,QAAQ,KAAK,QAAQ,IAAI,YAAY,CAAC;IAEtC;;;;;;;;;OASG;IACH,QAAQ,KAAK,SAAS,IAAI,SAAS,CAAC;IAEpC;;OAEG;IACH,QAAQ,CAAC,gBAAgB,IAAI,OAAO;CACrC"}