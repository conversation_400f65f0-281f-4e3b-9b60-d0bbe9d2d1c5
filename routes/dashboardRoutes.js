const express = require('express');
const { token } = require('../token');
const { generateMultiPdfReport } = require('../charts/generateMultiPdfReport');
const { generatePuppeteerReport } = require('../charts/generatePuppeteerReport');
const router = express.Router();

/**
 * GET /dashboard/:id
 * Retrieve dashboard data by ID
 * Mimics the OneIOT dashboard API structure
 */
router.get('/dashboard/:id/dynamic', async (req, res) => {
    // startResourceMonitoring();
    try {
        const dashboardId = req.params.id;

        // Validate dashboard ID
        if (!dashboardId || isNaN(dashboardId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid dashboard ID',
                error: 'BAD_REQUEST'
            });
        }

        // Simulate different responses based on ID
        if (dashboardId === '404') {
            return res.status(404).json({
                success: false,
                message: 'Dashboard not found',
                error: 'NOT_FOUND'
            });
        }

        const resp = await fetch("https://services-dev.oneiot.io/anl/api/dashboard/"
            + dashboardId, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const dashboardData = await resp.json()
        const dashlist = dashboardData?.data?.usersDashboard.dashboardList
        console.log({ dashboardData })
        if (!dashlist || !Array.isArray(dashlist) || dashlist.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Token is incorrect',
                error: 'DASHBOARD_NOT_FOUND',
                dashboardId: dashboardId
            });
        }

        // Generate multi-chart PDF report using the dashboard configuration
        try {
            const pdfPath = await generateMultiPdfReport(
                dashlist,
                token,
                `./output/dashboard-${dashboardId}-report.pdf`,
                {
                    from: '2025-05-31T18:30:00.000Z',
                    to: '2025-07-15T18:29:59.999Z'
                }
            );

            console.log(`PDF report generated: ${pdfPath}`);

            // Log the request (similar to the original curl)
            console.log(`Dashboard API called: ID=${dashboardId}, User-Agent=${req.get('User-Agent')}`);

            res.status(200).json({
                success: true,
                message: 'Dynamic dashboard PDF report generated successfully',
                pdfPath: pdfPath,
                dashboardId: dashboardId,
                chartsGenerated: dashlist.length,
                dashlist: dashlist
            });

        } catch (pdfError) {
            console.error('Error generating PDF report:', pdfError);

            // Still return dashboard data even if PDF generation fails
            res.status(200).json({
                success: true,
                message: 'Dashboard data retrieved, but PDF generation failed',
                error: pdfError.message,
                dashlist: dashlist
            });
        }

    } catch (error) {
        console.error('Error in dashboard endpoint:', error);

        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR',
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * GET /dashboard/:id/puppeteer
 * Generate PDF report using Puppeteer instead of jsPDF
 */
router.get('/dashboard/:id/puppeteer', async (req, res) => {
    try {
        const dashboardId = req.params.id;

        // Validate dashboard ID
        if (!dashboardId || isNaN(dashboardId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid dashboard ID',
                error: 'BAD_REQUEST'
            });
        }

        // Fetch dashboard data from API (same as dynamic route)
        const resp = await fetch("https://services-dev.oneiot.io/anl/api/dashboard/"
            + dashboardId, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const dashboardData = await resp.json();
        const dashlist = dashboardData?.data?.usersDashboard?.dashboardList;

        // Validate dashboard data
        if (!dashlist || !Array.isArray(dashlist) || dashlist.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Dashboard not found or empty',
                error: 'DASHBOARD_NOT_FOUND',
                dashboardId: dashboardId
            });
        }

        // Generate PDF using Puppeteer
        const outputPath = `./output/dashboard-${dashboardId}-puppeteer-report.pdf`;
        const pdfPath = await generatePuppeteerReport(dashlist, outputPath, {
            token: token,
            from: req.query.from || '2025-05-31T18:30:00.000Z',
            to: req.query.to || '2025-07-15T18:29:59.999Z'
        });

        res.json({
            success: true,
            message: 'Puppeteer PDF report generated successfully',
            pdfPath: pdfPath,
            dashboardId: dashboardId,
            chartsGenerated: dashlist.length,
            dashlist: dashlist
        });

    } catch (error) {
        console.error('Error in Puppeteer dashboard route:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR',
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
