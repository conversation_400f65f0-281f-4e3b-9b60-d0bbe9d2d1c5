import { DashboardGraphItem } from "@/index";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { format } from "date-fns";
import dayjs from "dayjs";
import { useMemo, useRef, useState, useEffect } from "react";
import {
  ComposedChart,
  Bar,
  Line,
  Area,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  ReferenceArea,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartConfig,
  ChartLegend,
} from "../../../components/shadcn/components/chart";
import { updateGraphDuration } from "../../../features/dashboardBuilderSlice";
import { updateExpandedDuration } from "../../../features/expandedGraphSlice";
import { useAppDispatch } from "../../../store";
import {
  formattedNumber,
  getDiffDate,
  selectDurationFromGraph,
} from "../utils";
import { editKeyToLabel } from "@utils/chartConfig";

/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * COMMON CHART COMPONENT - UNIVERSAL CHART RENDERER
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * This component handles multiple chart types using Recharts library:
 *
 * SUPPORTED CHART TYPES:
 * ┌─────────────────┬──────────────────┬─────────────────────────────────────┐
 * │ Chart Type      │ Implementation   │ Description                         │
 * ├─────────────────┼──────────────────┼─────────────────────────────────────┤
 * │ Bar             │ RechartsBarChart │ Vertical/Horizontal bar charts      │
 * │ Bar-H           │ RechartsBarChart │ Horizontal bar charts (legacy)     │
 * │ Line            │ RechartsLineChart│ Line charts with optional dots     │
 * │ Area            │ RechartsAreaChart│ Area charts with gradient fills    │
 * │ Scatter         │ RechartsScatter  │ Scatter plot charts                │
 * │ Range           │ RechartsLineChart│ Range charts (falls back to Line)  │
 * └─────────────────┴──────────────────┴─────────────────────────────────────┘
 *
 * CHART RENDERING LOGIC:
 * 1. ComposedChart (with zoom) - For interactive time series
 * 2. ScatterChart - For scatter plot data
 * 3. BarChart (Horizontal) - For horizontal bar charts
 * 4. BarChart (Vertical) - For vertical bar charts
 * 5. AreaChart - For area/filled line charts
 * 6. LineChart - Default fallback for Line/Range charts
 *
 * ═══════════════════════════════════════════════════════════════════════════════
 */

const MONTHS = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

function convertUTCStrDateToLocalDate(utcTimestamp: string) {
  const utcDate = new Date(utcTimestamp);
  const localDate = new Date(
    utcDate.getTime() - utcDate.getTimezoneOffset() * 60000
  );
  return `${MONTHS[localDate.getMonth()]} ${localDate.getDate()}`;
}

export const chartSecondaryColor = "hsl(215.4 16.3% 46.9%)";

interface ChartData {
  labels: string[];
  datasets: Array<{
    label?: string;
    data: number[] | Array<{ x: number | string; y: number }>;
    borderColor?: string;
    backgroundColor?: string;
    type?: string;
    key?: string;
    order?: number;
    thingName?: string;
  }>;
}
interface CommonChartProps {
  data: ChartData;
  stacked?: boolean;
  type?: "Bar" | "Line";
  modal?: boolean;
  indexAxis?: "x" | "y";
  timeSeries?: boolean;
  aggregation?: any;
  hidden?: any;
  aggregationType?: string;
  updateWidget?: any;
  size?: string;
  compareChartData?: {
    labels: string[];
    datasets: ChartData["datasets"];
    aggregations: Record<string, any>;
  };
  hideLabel?: boolean;
  anomolies?: ChartData["datasets"][number];
  labelMapping?: DashboardGraphItem["labelMapping"];
  chartType?: string;
  enableZoom?: boolean;
  showLegends?: boolean;
  chartLayoutData?: ReactGridLayout.Layout;
}

const CommonChart = ({
  data,
  stacked = false,
  type = "Bar",
  modal = false,
  indexAxis = "x",
  timeSeries,

  compareChartData,
  // aggregation,
  // hidden,
  // aggregationType,
  // updateWidget,
  // size,
  // hideLabel = false,
  // labelMapping,
  // anomolies,
  chartType,

  enableZoom = true,
  showLegends,
  chartLayoutData,
}: CommonChartProps) => {
  // console.log({  aggregation, aggregationType });
  const [stepSize, unit] = getDiffDate(data.labels);
  const dispatch = useAppDispatch();
  const chartRef = useRef<HTMLDivElement>(null);

  const shouldShowLegends = () => {
    if (!chartLayoutData) return true;

    if (showLegends !== undefined) return showLegends;
    return chartLayoutData?.w > 5 || chartLayoutData?.h > 5;
  };

  const [refAreaLeft, setRefAreaLeft] = useState<string | null>(null);
  const [refAreaRight, setRefAreaRight] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<string | null>(null);
  const [endTime, setEndTime] = useState<string | null>(null);
  const [originalData, setOriginalData] = useState<any[]>([]);
  const [isSelecting, setIsSelecting] = useState(false);

  const updateDuration = (
    duration: { title: string; value: string } | null
  ) => {
    if (duration) {
      if (modal) {
        dispatch(updateExpandedDuration(duration));
      } else {
        dispatch(updateGraphDuration(duration));
        dispatch(updateExpandedDuration(duration));
      }
    }
  };

  const chartData = useMemo(() => {
    const transformedData = data.labels.map((label, index) => {
      const dataPoint: any = { label, index };

      data.datasets.forEach((dataset, datasetIndex) => {
        const value = Array.isArray(dataset.data)
          ? typeof dataset.data[index] === "object"
            ? (dataset.data[index] as any)?.y
            : dataset.data[index]
          : 0;

        dataPoint[dataset.label || `dataset${datasetIndex}`] = value || 0;
      });

      // Add compare chart data if available
      if (compareChartData) {
        compareChartData.datasets.forEach((dataset, datasetIndex) => {
          const value = Array.isArray(dataset.data)
            ? typeof dataset.data[index] === "object"
              ? (dataset.data[index] as any)?.y
              : dataset.data[index]
            : 0;

          dataPoint[`compare_${dataset.label || `dataset${datasetIndex}`}`] =
            value || 0;
        });
      }

      return dataPoint;
    });

    return transformedData;
  }, [data, compareChartData]);

  useEffect(() => {
    if (chartData.length > 0) {
      setOriginalData(chartData);
      if (timeSeries && data.labels.length > 0) {
        setStartTime(data.labels[0]);
        setEndTime(data.labels[data.labels.length - 1]);
      }
    }
  }, [chartData, timeSeries, data.labels]);

  useEffect(() => {
    if (!enableZoom || !chartRef.current) return;

    const chartElement = chartRef.current;

    const preventBrowserZoom = (e: Event) => {
      if (
        e.type === "touchstart" ||
        e.type === "touchmove" ||
        e.type === "wheel"
      ) {
        e.preventDefault();
      }
    };

    const addEventListenerSafely = (event: string, handler: EventListener) => {
      try {
        chartElement.addEventListener(event, handler, { passive: false });
      } catch (error) {
        chartElement.addEventListener(event, handler, { passive: true });
      }
    };

    addEventListenerSafely("touchstart", preventBrowserZoom);
    addEventListenerSafely("touchmove", preventBrowserZoom);
    addEventListenerSafely("wheel", preventBrowserZoom);

    return () => {
      chartElement.removeEventListener("touchstart", preventBrowserZoom);
      chartElement.removeEventListener("touchmove", preventBrowserZoom);
      chartElement.removeEventListener("wheel", preventBrowserZoom);
    };
  }, [enableZoom]);

  const zoomedData = useMemo(() => {
    if (!enableZoom || !timeSeries || !startTime || !endTime) {
      return chartData;
    }

    const dataPointsInRange = originalData.filter((_, index) => {
      const label = data.labels[index];
      return label >= startTime && label <= endTime;
    });

    return dataPointsInRange.length > 1
      ? dataPointsInRange
      : originalData.slice(0, 2);
  }, [
    enableZoom,
    timeSeries,
    startTime,
    endTime,
    originalData,
    chartData,
    data.labels,
  ]);

  // CREATING CHART CONFIG
  const chartConfig: ChartConfig = useMemo(() => {
    const config: ChartConfig = {};

    data.datasets.forEach((dataset, index) => {
      const key = dataset.label || `dataset${index}`;
      config[key] = {
        label: key,
        color:
          dataset.borderColor ||
          dataset.backgroundColor ||
          `hsl(${(index * 137.5) % 360}, 70%, 50%)`,
      };
    });

    if (compareChartData) {
      compareChartData.datasets.forEach((dataset, index) => {
        const key = `compare_${dataset.label || `dataset${index}`}`;
        config[key] = {
          label: dataset.label || `Compare ${index}`,
          color:
            dataset.borderColor ||
            dataset.backgroundColor ||
            `hsl(${((index + data.datasets.length) * 137.5) % 360}, 70%, 50%)`,
        };
      });
    }

    return config;
  }, [data.datasets, compareChartData]);

  // Format tick values for time series
  const formatXAxisTick = (value: any) => {
    if (!timeSeries) return value;

    const currentDate = new Date(value);

    if (unit === "month") {
      return format(currentDate, "MMM");
    }

    return convertUTCStrDateToLocalDate(value);
  };

  const handleChartClick = (data: any) => {
    if (enableZoom) return;

    if (unit === "second" && stepSize < 30) {
      return;
    }

    if (data && data.activeLabel) {
      const labelIndex = zoomedData.findIndex(
        (item) => item.label === data.activeLabel
      );
      if (labelIndex >= 0) {
        const element = [{ index: labelIndex }];
        const duration = selectDurationFromGraph({
          element,
          labels: data.labels,
          unit,
          stepSize,
        });
        updateDuration(duration);
      }
    }
  };

  // ZOOM HANDLERS
  const handleMouseDown = (e: any) => {
    if (enableZoom && e.activeLabel) {
      setRefAreaLeft(e.activeLabel);
      setIsSelecting(true);
    }
  };

  const handleMouseMove = (e: any) => {
    if (enableZoom && isSelecting && e.activeLabel) {
      setRefAreaRight(e.activeLabel);
    }
  };

  const handleMouseUp = () => {
    if (enableZoom && refAreaLeft && refAreaRight) {
      const [left, right] = [refAreaLeft, refAreaRight].sort();
      setStartTime(left);
      setEndTime(right);
    }
    setRefAreaLeft(null);
    setRefAreaRight(null);
    setIsSelecting(false);
  };

  const handleZoom = (
    e: React.WheelEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
  ) => {
    if (!enableZoom || !timeSeries || !originalData.length || !chartRef.current)
      return;

    let zoomFactor = 0.1;
    let direction = 0;
    let clientX = 0;

    if ("deltaY" in e) {
      zoomFactor = 0.1;

      // Pinch zoom on macOS has ctrlKey true and reversed direction
      const isPinchGesture = e.ctrlKey;
      direction = (isPinchGesture ? e.deltaY > 0 : e.deltaY < 0) ? -1 : 1;

      clientX = e.clientX;
    } else {
      console.log("zoom not working");
      return;
    }

    const currentRange =
      new Date(endTime || data.labels[data.labels.length - 1]).getTime() -
      new Date(startTime || data.labels[0]).getTime();

    const zoomAmount = currentRange * zoomFactor * direction;
    const chartRect = chartRef.current.getBoundingClientRect();
    const mouseX = clientX - chartRect.left;
    const chartWidth = chartRect.width;
    const mousePercentage = mouseX / chartWidth;

    const currentStartTime = new Date(startTime || data.labels[0]).getTime();
    const currentEndTime = new Date(
      endTime || data.labels[data.labels.length - 1]
    ).getTime();

    const newStartTime = new Date(
      currentStartTime + zoomAmount * mousePercentage
    );
    const newEndTime = new Date(
      currentEndTime - zoomAmount * (1 - mousePercentage)
    );

    // Find the closest labels to the new times
    const newStartLabel = data.labels.reduce((prev, curr) => {
      return Math.abs(new Date(curr).getTime() - newStartTime.getTime()) <
        Math.abs(new Date(prev).getTime() - newStartTime.getTime())
        ? curr
        : prev;
    });

    const newEndLabel = data.labels.reduce((prev, curr) => {
      return Math.abs(new Date(curr).getTime() - newEndTime.getTime()) <
        Math.abs(new Date(prev).getTime() - newEndTime.getTime())
        ? curr
        : prev;
    });

    const newStartIndex = data.labels.indexOf(newStartLabel);
    const newEndIndex = data.labels.indexOf(newEndLabel);

    // Ensure we have a valid range

    if (newEndIndex > newStartIndex && newEndIndex - newStartIndex >= 1) {
      setStartTime(newStartLabel);
      setEndTime(newEndLabel);
    }
  };

  // Custom tooltip content
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null;
    }

    const getTooltipTitle = () => {
      if (!timeSeries) return "";

      const timestamp = label;
      const labelIndex = chartData.findIndex((item) => item.label === label);
      const nextTimestamp =
        labelIndex >= 0 ? data.labels[labelIndex + 1] : null;

      if (unit === "month") {
        return dayjs(timestamp).format("MMM YYYY");
      }
      if (unit === "day") {
        if (stepSize === 1 || !nextTimestamp) {
          return convetUTCToLocal(timestamp, "MMM DD, YYYY");
        }
        return `${convetUTCToLocal(
          timestamp,
          "MMM DD, YYYY"
        )} - ${convetUTCToLocal(nextTimestamp, "MMM DD, YYYY")}`;
      }
      if (unit === "hour") {
        if (stepSize === 1 || !nextTimestamp) {
          return convetUTCToLocal(timestamp, "MMM DD HH:mm");
        }
        return `${convetUTCToLocal(
          timestamp,
          "MMM DD HH:mm"
        )} - ${convetUTCToLocal(nextTimestamp, "MMM DD HH:mm")}`;
      }
      return convetUTCToLocal(timestamp);
    };

    return (
      <div className="bg-background border border-border rounded-lg p-2 shadow-lg">
        {timeSeries && (
          <div className="font-medium text-sm mb-1">{getTooltipTitle()}</div>
        )}
        {payload.map((entry: any, index: number) => {
          // Handle anomaly data
          if (entry.dataKey === "label") return null;
          if (entry.dataKey === "anomalies" && entry.payload) {
            const point = entry.payload;
            return (
              <div key={index} className="flex items-center gap-2 text-xs">
                <div
                  className="w-2 h-2 rounded-sm"
                  style={{ backgroundColor: "#E80000" }}
                />
                <span>
                  Anomaly {point.thingName} ({editKeyToLabel(point.key)}):{" "}
                  {formattedNumber(point.y)}
                </span>
              </div>
            );
          }

          const entryName = entry.name || entry.dataKey || `Dataset ${index}`;
          const displayName = editKeyToLabel(entryName);

          // For scatter charts, the value might be in a different structure
          const displayValue =
            entry.value !== undefined
              ? entry.value
              : entry.payload?.[entry.dataKey] !== undefined
              ? entry.payload[entry.dataKey]
              : 0;

          // Fix color resolution for all chart types
          let entryColor = entry.color;
          const dataKey = entry.dataKey || entry.name;
          if (
            !entryColor ||
            entryColor === "undefined" ||
            entryColor === "#8884d8"
          ) {
            // First, try to find from chartConfig
            entryColor = chartConfig[dataKey]?.color;

            // If not found, try to find the dataset
            if (!entryColor) {
              const dataset = data.datasets.find((d, idx) => {
                const datasetKey = d.label || `dataset${idx}`;
                return datasetKey === dataKey;
              });
              entryColor = dataset?.borderColor || dataset?.backgroundColor;
            }

            if (!entryColor) {
              entryColor = `hsl(${(index * 137.5) % 360}, 70%, 50%)`;
            }
          }

          return (
            <div key={index} className="flex items-center gap-2 text-xs">
              <div
                className="w-2 h-2 rounded-sm"
                style={{ backgroundColor: entryColor }}
              />
              <span>
                {displayName}: {formattedNumber(displayValue)}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <div
        className={`w-full h-full dashboard-item-content ${
          enableZoom ? "overflow-hidden" : ""
        }`}
        style={
          {
            ...(enableZoom && {
              touchAction: "none",
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
            }),
          } as React.CSSProperties
        }
      >
        <div
          className={`w-full h-full ${enableZoom ? "select-none" : ""}`}
          onWheel={enableZoom ? handleZoom : undefined}
          onTouchMove={enableZoom ? handleZoom : undefined}
          ref={chartRef}
          style={
            {
              touchAction: enableZoom ? "none" : "auto",
              userSelect: enableZoom ? "none" : "auto",
              WebkitUserSelect: enableZoom ? "none" : "auto",
              MozUserSelect: enableZoom ? "none" : "auto",
              ...(enableZoom && { msUserSelect: "none" }),
            } as React.CSSProperties
          }
        >
          <ChartContainer
            config={chartConfig}
            className="w-full h-full -ml-[20px]"
          >
            <ComposedChart
              data={zoomedData}
              onClick={handleChartClick}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              layout={indexAxis === "y" ? "vertical" : "horizontal"}
            >
              <defs>
                {data.datasets.map((dataset, index) => {
                  const datasetKey = dataset.label || `dataset${index}`;
                  const configColor = chartConfig[datasetKey]?.color;
                  const fallbackColor = `hsl(${
                    (index * 137.5) % 360
                  }, 70%, 50%)`;

                  let color =
                    dataset.borderColor ||
                    dataset.backgroundColor ||
                    configColor ||
                    fallbackColor;

                  // Convert RGB to hex for better SVG compatibility
                  if (color && color.startsWith("rgb(")) {
                    const rgbMatch = color.match(
                      /rgb\((\d+),\s*(\d+),\s*(\d+)\)/
                    );
                    if (rgbMatch) {
                      const r = parseInt(rgbMatch[1]);
                      const g = parseInt(rgbMatch[2]);
                      const b = parseInt(rgbMatch[3]);
                      color = `#${r.toString(16).padStart(2, "0")}${g
                        .toString(16)
                        .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
                    }
                  }

                  return (
                    <linearGradient
                      key={`gradient-${dataset.label || `dataset${index}`}`}
                      id={`gradient-${dataset.label || `dataset${index}`}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor={color} stopOpacity={0.2} />
                      <stop offset="50%" stopColor={color} stopOpacity={0.1} />
                      <stop offset="100%" stopColor={color} stopOpacity={0.0} />
                    </linearGradient>
                  );
                })}
              </defs>

              <CartesianGrid vertical={false} stroke="hsl(var(--border))" />
              <XAxis
                type={
                  type === "Bar" && indexAxis === "y" ? "number" : "category"
                }
                dataKey={
                  type === "Bar" && indexAxis === "y" ? undefined : "label"
                }
                tickFormatter={
                  type === "Bar" && indexAxis === "y"
                    ? (value) => formattedNumber(value)
                    : formatXAxisTick
                }
                stroke={chartSecondaryColor}
                fontSize={12}
                tickLine={false}
                domain={indexAxis == "y" ? [0, "dataMax"] : []}
              />
              <YAxis
                type={
                  type === "Bar" && indexAxis === "y" ? "category" : "number"
                }
                dataKey={
                  type === "Bar" && indexAxis === "y" ? "label" : undefined
                }
                tickFormatter={
                  type === "Bar" && indexAxis === "y"
                    ? formatXAxisTick
                    : (value) => formattedNumber(value)
                }
                stroke={chartSecondaryColor}
                fontSize={12}
              />
              <ChartTooltip content={<CustomTooltip />} />

              {type === "Bar"
                ? data.datasets.map((dataset, index) => {
                    return (
                      <Bar
                        key={dataset.label || `dataset${index}`}
                        dataKey={dataset.label || `dataset${index}`}
                        fill={
                          dataset.borderColor ||
                          chartConfig[dataset.label || `dataset${index}`]?.color
                        }
                        stackId={stacked ? "stack" : undefined}
                        radius={index == 0 ? [3, 3, 0, 0] : 0}
                      />
                    );
                  })
                : chartType === "Area"
                ? data.datasets.map((dataset, index) => (
                    <Area
                      key={dataset.label || `dataset${index}`}
                      type="monotone"
                      dataKey={dataset.label || `dataset${index}`}
                      stroke={
                        dataset.borderColor ||
                        chartConfig[dataset.label || `dataset${index}`]?.color
                      }
                      strokeWidth={2}
                      fill={`url(#gradient-${
                        dataset.label || `dataset${index}`
                      })`}
                      fillOpacity={1}
                      connectNulls={false}
                    />
                  ))
                : chartType === "Scatter"
                ? data.datasets.map((dataset, index) => (
                    <Scatter
                      key={dataset.label || `dataset${index}`}
                      dataKey={dataset.label || `dataset${index}`}
                      fill={
                        dataset.borderColor ||
                        chartConfig[dataset.label || `dataset${index}`]
                          ?.color ||
                        "#8884d8"
                      }
                      line={false}
                      shape="circle"
                    />
                  ))
                : data.datasets.map((dataset, index) => (
                    <Line
                      key={dataset.label || `dataset${index}`}
                      type={chartType == "Step" ? "step" : "monotone"}
                      dataKey={dataset.label || `dataset${index}`}
                      stroke={
                        dataset.borderColor ||
                        chartConfig[dataset.label || `dataset${index}`]?.color
                      }
                      strokeWidth={2}
                      dot={{ r: 1 }}
                      connectNulls={false}
                    />
                  ))}

              {refAreaLeft && refAreaRight && (
                <ReferenceArea
                  x1={refAreaLeft}
                  x2={refAreaRight}
                  strokeOpacity={0.3}
                  fill="hsl(var(--foreground))"
                  fillOpacity={0.05}
                />
              )}

              {shouldShowLegends() && (
                <ChartLegend
                  content={({ payload }) => {
                    return (
                      <div className="flex flex-wrap gap-2 justify-center">
                        {payload?.map((entry, index) => {
                          if (
                            entry?.value?.includes("_min") ||
                            entry.value?.includes("_max")
                          )
                            return null;
                          return (
                            <div
                              key={index}
                              className="flex items-center gap-1 text-xs"
                            >
                              <div
                                className="w-2 h-2 rounded-sm"
                                style={{ backgroundColor: entry.color }}
                              />
                              <span>{editKeyToLabel(entry.value)}</span>
                            </div>
                          );
                        })}
                      </div>
                    );
                  }}
                />
              )}
            </ComposedChart>
          </ChartContainer>
        </div>
      </div>
    </>
  );
};

export default CommonChart;
 