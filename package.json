{"name": "backend-charting", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chart", "express", "node", "cron"], "author": "", "license": "ISC", "description": "Automated chart generation backend with Express and Chart.js", "dependencies": {"axios": "^1.11.0", "canvas": "^3.1.2", "chart.js": "^4.5.0", "chartjs-node-canvas": "^5.0.0", "express": "^4.21.2", "jspdf": "^3.0.1", "node-cron": "^4.2.1", "nodemon": "^3.1.10", "puppeteer": "^24.15.0"}}