const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
const fs = require('fs');
const path = require('path');
const { jsPDF } = require('jspdf');

/**
 * Load logo image and convert to base64, also get dimensions
 * @returns {Object|null} - Object with base64 data and dimensions, or null if not found
 */
function loadImage(path = './assets/logo.png') {
    try {
        const logoPath = path.resolve();

        if (fs.existsSync(logoPath)) {
            const logoBuffer = fs.readFileSync(logoPath);
            const base64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;

            // Get image dimensions using a simple PNG header parser
            let width = 100, height = 100; // Default fallback
            try {
                // PNG files have width and height at bytes 16-23
                if (logoBuffer.length > 24 && logoBuffer.toString('ascii', 1, 4) === 'PNG') {
                    width = logoBuffer.readUInt32BE(16);
                    height = logoBuffer.readUInt32BE(20);
                }
            } catch (e) {
                console.warn('Could not read PNG dimensions, using defaults');
            }

            return { base64, width, height };
        } else {
            console.warn('file not found at:', logoPath);
            return null;
        }
    } catch (error) {
        console.error('Error loading logo:', error);
        return null;
    }
}




/**
 * Create the first page with logo and title
 * @param {jsPDF} pdf - PDF document instance
 * @param {Object} logoData - Logo data with base64 and dimensions
 */
function createFirstPage(pdf, logoData, top, bottom) {
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Add logo in center if available
    if (logoData && logoData.base64) {
        // Calculate logo dimensions maintaining aspect ratio
        const maxLogoWidth = 120;
        const maxLogoHeight = 100;
        const aspectRatio = logoData.width / logoData.height;

        let logoWidth = maxLogoWidth;
        let logoHeight = logoWidth / aspectRatio;

        // If height exceeds max, scale down
        if (logoHeight > maxLogoHeight) {
            logoHeight = maxLogoHeight;
            logoWidth = logoHeight * aspectRatio;
        }

        const logoX = (pageWidth - logoWidth) / 2;
        const logoY = (pageHeight - logoHeight) / 2 - 40;

        pdf.addImage(logoData.base64, 'PNG', logoX, logoY, logoWidth, logoHeight);
        pdf.addImage(top.base64, 'PNG', 0, 0, logoWidth, logoHeight);
        pdf.addImage(bottom.base64, 'PNG', pageWidth - logoWidth, pageWidth - logoHeight, logoWidth, logoHeight);

    }

    // Add title below logo
    pdf.setFontSize(32);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80); // Dark blue-gray
    pdf.text('Dynamic Scheduled Reports', pageWidth / 2, pageHeight / 2 + 30, { align: 'center' });

    // Add subtitle
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(127, 140, 141); // Gray
    pdf.text('Automated IoT Analytics Dashboard', pageWidth / 2, pageHeight / 2 + 50, { align: 'center' });

    // Add generation info at bottom
    pdf.setFontSize(12);
    pdf.setTextColor(149, 165, 166); // Light gray
    const currentDate = new Date().toLocaleString();
    pdf.text(`Generated on: ${currentDate}`, pageWidth / 2, pageHeight - 30, { align: 'center' });
}

/**
 * Add header to page with logo
 * @param {jsPDF} pdf - PDF document instance
 * @param {Object} logoData - Logo data with base64 and dimensions
 */
function addHeader(pdf, logoData) {
    const pageWidth = pdf.internal.pageSize.getWidth();

    // Add logo to top left
    if (logoData && logoData.base64) {
        // Calculate header logo dimensions maintaining aspect ratio
        const maxHeaderWidth = 25;
        const maxHeaderHeight = 18;
        const aspectRatio = logoData.width / logoData.height;

        let logoWidth = maxHeaderWidth;
        let logoHeight = logoWidth / aspectRatio;

        // If height exceeds max, scale down
        if (logoHeight > maxHeaderHeight) {
            logoHeight = maxHeaderHeight;
            logoWidth = logoHeight * aspectRatio;
        }

        pdf.addImage(logoData.base64, 'PNG', 15, 10, logoWidth, logoHeight);
    }

    // Add header line
    pdf.setDrawColor(52, 73, 94); // Dark blue-gray
    pdf.setLineWidth(0.5);
    pdf.line(15, 25, pageWidth - 15, 25);
}

/**
 * Add footer to page with page number and date
 * @param {jsPDF} pdf - PDF document instance
 * @param {number} pageNumber - Current page number
 * @param {string} createdAt - Creation date string
 */
function addFooter(pdf, pageNumber, createdAt) {
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Add footer line
    pdf.setDrawColor(52, 73, 94); // Dark blue-gray
    pdf.setLineWidth(0.5);
    pdf.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add page number and date
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(127, 140, 141); // Gray

    const footerText = `Page ${pageNumber} | Generated: ${createdAt}`;
    pdf.text(footerText, pageWidth - 15, pageHeight - 10, { align: 'right' });
}

/**
 * Create report summary page
 * @param {jsPDF} pdf - PDF document instance
 * @param {Object} logoData - Logo data with base64 and dimensions
 * @param {Array} dashboardList - Array of dashboard configurations
 * @param {string} from - Start date
 * @param {string} to - End date
 * @param {string} createdAt - Creation date string
 */
function createReportSummaryPage(pdf, logoData, dashboardList, from, to, createdAt) {
    const pageWidth = pdf.internal.pageSize.getWidth();

    // Add header
    addHeader(pdf, logoData);

    // Add page title
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80); // Dark blue-gray
    pdf.text('Report Summary', 20, 55);

    // Add report info section
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(52, 73, 94);
    pdf.text('Report Information', 20, 80);

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(44, 62, 80);

    let yPos = 95;
    pdf.text(`• Data Period: ${new Date(from).toLocaleDateString()} - ${new Date(to).toLocaleDateString()}`, 25, yPos);
    yPos += 15;
    pdf.text(`• Total Charts: ${dashboardList.length}`, 25, yPos);
    yPos += 15;
    pdf.text(`• Generated: ${createdAt}`, 25, yPos);
    yPos += 25;

    // Add dashboard summary section
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(52, 73, 94);
    pdf.text('Dashboard Components', 20, yPos);
    yPos += 20;

    // Group charts by type
    const chartTypes = {};
    dashboardList.forEach(config => {
        const type = config.chartType;
        if (!chartTypes[type]) {
            chartTypes[type] = [];
        }
        chartTypes[type].push(config);
    });

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(44, 62, 80);

    Object.keys(chartTypes).forEach(type => {
        const charts = chartTypes[type];
        pdf.text(`• ${type} Charts: ${charts.length}`, 25, yPos);
        yPos += 12;

        // List chart names (limit to avoid overflow)
        const chartNames = charts.slice(0, 3).map(c => c.label).join(', ');
        const additionalCount = charts.length > 3 ? ` (+${charts.length - 3} more)` : '';
        pdf.setFontSize(9);
        pdf.setTextColor(127, 140, 141);
        pdf.text(`  ${chartNames}${additionalCount}`, 30, yPos);
        pdf.setFontSize(11);
        pdf.setTextColor(44, 62, 80);
        yPos += 15;
    });

    // Add footer
    addFooter(pdf, 2, createdAt);
}
const { formatTimestamp, hexToRgba } = require('../utils');
const { COLOR_SCHEMES } = require('../colors');

const width = 1200;  // Increased from 800
const height = 600;  // Increased from 400
const backgroundColour = 'white';

// Create ChartJS instance with high-quality settings
const chartJSNodeCanvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour,
    chartCallback: (ChartJS) => {
        ChartJS.defaults.font.family = 'Arial, sans-serif';
        ChartJS.defaults.font.size = 12;
    }
});

/**
 * Build dynamic API URL based on dashboard configuration
 * @param {Object} dashConfig - Dashboard configuration object
 * @param {string} baseUrl - Base API URL
 * @param {string} from - Start date
 * @param {string} to - End date
 * @returns {string} - Complete API URL
 */
function buildApiUrl(dashConfig, baseUrl, from, to) {
    // Use different endpoint for table charts
    if (dashConfig.chartType.toLowerCase() === 'table') {
        const tableBaseUrl = 'https://services-dev.oneiot.io/anl/api/stats/ts/table';
        const params = new URLSearchParams();

        // Add filters as JSON string
        if (dashConfig.filters && dashConfig.filters.length > 0) {
            params.append('filters', JSON.stringify(dashConfig.filters));
        }

        // Add date range
        params.append('from', from);
        params.append('to', to);

        return `${tableBaseUrl}?${params.toString()}`;
    }

    // For non-table charts, use the original logic
    const params = new URLSearchParams();

    // Add product name from filters
    const productFilter = dashConfig.filters?.find(f => f.field === 'productName');
    if (productFilter && productFilter.value?.length > 0) {
        params.append('productName', productFilter.value[0]);
    }

    // Add date range
    params.append('from', from);
    params.append('to', to);

    // Add anomalies flag
    params.append('anomalies', 'true');

    // Add data keys
    if (dashConfig.dataKey) {
        params.append('key', dashConfig.dataKey);
    }

    return `${baseUrl}?${params.toString()}`;
}

/**
 * Generate table image using canvas
 * @param {Object} chartConfig - Chart configuration from dashboard
 * @param {Object} chartData - Data from API response
 * @returns {Promise<Buffer>} - Table image buffer
 */
async function generateTableImage(chartConfig, chartData) {
    return await generateCanvasTable(chartConfig, chartData);
}

/**
 * Generate a proper table image using canvas with actual data
 * @param {Object} chartConfig - Chart configuration from dashboard
 * @param {Object} chartData - Data from API response
 * @returns {Promise<Buffer>} - Table image buffer
 */
async function generateCanvasTable(chartConfig, chartData) {
    try {
        const canvas = require('canvas');

        // Handle different data structures for table API vs regular API
        let tableData, columns;

        // Check if chartData exists and has the expected structure
        if (!chartData) {
            console.error('No chart data received for table');
            tableData = [];
            columns = [];
        } else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
            // Table API response format: { data: { data: [...] } }
            console.log('Using nested table API response format');
            const rawData = chartData.data.data;

            // Flatten the data structure - combine top-level fields with payload
            tableData = rawData.map(item => {
                const flattened = {
                    thingName: item.thingName,
                    productName: item.productName,
                    timestamp: item.timestamp,
                    uniqueId: item.uniqueId
                };

                // Add payload fields to the flattened object
                if (item.payload) {
                    Object.keys(item.payload).forEach(key => {
                        flattened[key] = item.payload[key];
                    });
                }

                return flattened;
            });

            // Get all unique columns from flattened data
            const allColumns = new Set();
            tableData.forEach(row => {
                Object.keys(row).forEach(key => allColumns.add(key));
            });

            // Prioritize important columns and limit total columns for readability
            const priorityColumns = ['thingName', 'timestamp', 'speed', 'body_angle', 'engine_rpm', 'fuel_level', 'engine_coolant_temperature', 'engine_oil_pressure'];
            const otherColumns = Array.from(allColumns).filter(col => !priorityColumns.includes(col));

            // Limit to first 8-10 columns for better display
            columns = [...priorityColumns.filter(col => allColumns.has(col)), ...otherColumns.slice(0, 6)].slice(0, 10);

        } else if (chartData.success && Array.isArray(chartData.data)) {
            // Table API response format with success wrapper
            console.log('Using table API response format with success wrapper');
            tableData = chartData.data;
            columns = tableData.length > 0 ? Object.keys(tableData[0]) : [];
        } else if (chartData.data && Array.isArray(chartData.data)) {
            // Table API response format
            console.log('Using table API response format');
            tableData = chartData.data;
            columns = tableData.length > 0 ? Object.keys(tableData[0]) : [];
        } else if (chartData.data && chartData.data.labels && chartData.data.datapointsMap) {
            // Regular API response format
            console.log('Using regular API response format');
            const { labels, datapointsMap } = chartData.data;

            // If dataKey is empty, use all available keys from datapointsMap
            let dataKeys;
            if (!chartConfig.dataKey || chartConfig.dataKey.trim() === '') {
                dataKeys = Object.keys(datapointsMap);
                console.log('No dataKey specified, using all available keys:', dataKeys);
            } else {
                dataKeys = chartConfig.dataKey.split(',').map(key => key.trim());
            }

            // Convert to table format
            tableData = labels.map((label, index) => {
                const row = { timestamp: label };
                dataKeys.forEach(key => {
                    row[key] = datapointsMap[key] ? datapointsMap[key][index] : 'N/A';
                });
                return row;
            });
            columns = ['timestamp', ...dataKeys];
        } else if (Array.isArray(chartData)) {
            // Direct array response
            console.log('Using direct array response format');
            tableData = chartData;
            columns = tableData.length > 0 ? Object.keys(tableData[0]) : [];
        } else {
            // No valid data or unknown format
            console.error('Unknown data format for table:', chartData);
            console.error('Available keys in chartData:', Object.keys(chartData || {}));
            tableData = [];
            columns = [];
        }

        const canvasWidth = 1200;
        const canvasHeight = Math.max(600, 100 + (tableData.length + 2) * 30); // Dynamic height based on data

        const canvasInstance = canvas.createCanvas(canvasWidth, canvasHeight);
        const ctx = canvasInstance.getContext('2d');

        // Set background
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // Title
        ctx.fillStyle = '#333';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${chartConfig.label} - TABLE`, canvasWidth / 2, 40);

        if (tableData.length === 0 || columns.length === 0) {
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.fillText('No data available for this table', canvasWidth / 2, canvasHeight / 2);
            return canvasInstance.toBuffer('image/png');
        }

        // Table setup - adjust for number of columns
        const startY = 80;
        const rowHeight = 25;
        const minColWidth = 80;
        const maxColWidth = 150;
        const availableWidth = canvasWidth - 100;

        // Calculate column width based on number of columns
        let colWidth = availableWidth / columns.length;
        if (colWidth < minColWidth) {
            colWidth = minColWidth;
        } else if (colWidth > maxColWidth) {
            colWidth = maxColWidth;
        }

        // Adjust canvas width if needed for many columns
        const actualTableWidth = colWidth * columns.length;
        const adjustedCanvasWidth = Math.max(canvasWidth, actualTableWidth + 100);

        if (adjustedCanvasWidth > canvasWidth) {
            // Recreate canvas with adjusted width
            const newCanvasInstance = canvas.createCanvas(adjustedCanvasWidth, canvasHeight);
            const newCtx = newCanvasInstance.getContext('2d');

            // Set background
            newCtx.fillStyle = 'white';
            newCtx.fillRect(0, 0, adjustedCanvasWidth, canvasHeight);

            // Title
            newCtx.fillStyle = '#333';
            newCtx.font = 'bold 24px Arial';
            newCtx.textAlign = 'center';
            newCtx.fillText(`${chartConfig.label} - TABLE`, adjustedCanvasWidth / 2, 40);

            ctx = newCtx;
            canvasInstance = newCanvasInstance;
        }

        // Header row
        ctx.fillStyle = '#4CAF50';
        ctx.fillRect(50, startY, actualTableWidth, rowHeight);

        ctx.fillStyle = 'white';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';

        // Column headers
        columns.forEach((column, index) => {
            const x = 50 + index * colWidth + colWidth / 2;
            let displayName;

            // Custom display names for common columns
            switch (column) {
                case 'thingName': displayName = 'TRUCK'; break;
                case 'timestamp': displayName = 'TIME'; break;
                case 'speed': displayName = 'SPEED'; break;
                case 'body_angle': displayName = 'BODY ANGLE'; break;
                case 'engine_rpm': displayName = 'RPM'; break;
                case 'fuel_level': displayName = 'FUEL'; break;
                case 'engine_coolant_temperature': displayName = 'COOLANT'; break;
                case 'engine_oil_pressure': displayName = 'OIL PRESS'; break;
                default:
                    displayName = column.replace(/_/g, ' ').toUpperCase();
                    // Truncate long header names
                    if (displayName.length > 10) {
                        displayName = displayName.substring(0, 8) + '..';
                    }
            }

            ctx.fillText(displayName, x, startY + 17);
        });

        // Data rows (limit to first 20 rows to fit in canvas)
        const maxRows = Math.min(tableData.length, 20);
        for (let i = 0; i < maxRows; i++) {
            const y = startY + (i + 1) * rowHeight;
            const row = tableData[i];

            // Alternate row colors
            ctx.fillStyle = i % 2 === 0 ? '#f9f9f9' : 'white';
            ctx.fillRect(50, y, actualTableWidth, rowHeight);

            // Row border
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 0.5;
            ctx.strokeRect(50, y, actualTableWidth, rowHeight);

            // Data cells
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';

            columns.forEach((column, colIndex) => {
                const value = row[column];
                let displayValue;

                if (column === 'timestamp' && value) {
                    // Format timestamp to be more readable
                    try {
                        const date = new Date(value);
                        displayValue = date.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                    } catch (e) {
                        displayValue = value.toString().substring(11, 19); // Extract time part
                    }
                } else if (column === 'thingName' && value) {
                    // Shorten truck names
                    displayValue = value.replace('haultruck_', 'T');
                } else if (typeof value === 'string' && !isNaN(parseFloat(value))) {
                    // Handle numeric strings from payload
                    const numValue = parseFloat(value);
                    displayValue = numValue.toFixed(chartConfig.decimal || 1);
                } else if (typeof value === 'number') {
                    displayValue = value.toFixed(chartConfig.decimal || 1);
                } else if (typeof value === 'string' && value.length > 12) {
                    // Truncate long strings
                    displayValue = value.substring(0, 10) + '..';
                } else {
                    displayValue = value || 'N/A';
                }

                const x = 50 + colIndex * colWidth + colWidth / 2;

                // Add column borders
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 0.5;
                if (colIndex > 0) {
                    ctx.beginPath();
                    ctx.moveTo(50 + colIndex * colWidth, y);
                    ctx.lineTo(50 + colIndex * colWidth, y + rowHeight);
                    ctx.stroke();
                }

                ctx.fillText(displayValue, x, y + 15);
            });
        }

        // Footer info
        ctx.fillStyle = '#666';
        ctx.font = '11px Arial';
        ctx.textAlign = 'center';
        const footerY = startY + (maxRows + 1) * rowHeight + 20;
        const canvasCenter = adjustedCanvasWidth ? adjustedCanvasWidth / 2 : canvasWidth / 2;
        ctx.fillText(`Showing ${maxRows} of ${tableData.length} records | Decimal places: ${chartConfig.decimal || 2}`, canvasCenter, footerY);

        // Add column count info
        ctx.fillText(`Columns: ${columns.length}`, canvasCenter, footerY + 15);

        return canvasInstance.toBuffer('image/png');

    } catch (error) {
        console.error('Error generating canvas table:', error);
        throw error;
    }
}

/**
 * Generate dynamic chart based on chart type and configuration
 * @param {Object} chartConfig - Chart configuration from dashboard
 * @param {Object} chartData - Data from API response
 * @param {string} colorScheme - Color scheme to use
 * @returns {Promise<Buffer>} - Chart image buffer
 */
async function generateDynamicChart(chartConfig, chartData, colorScheme = 'mixed') {
    // Handle table type separately - this should not reach here, but adding safety check
    if (chartConfig.chartType.toLowerCase() === 'table') {
        return await generateTableImage(chartConfig, chartData);
    }

    // Ensure we have the expected data structure for regular charts
    if (!chartData.data || !chartData.data.labels || !chartData.data.datapointsMap) {
        throw new Error(`Invalid chart data structure for ${chartConfig.chartType} chart. Expected labels and datapointsMap.`);
    }

    const { labels, datapointsMap } = chartData.data;
    const formattedLabels = labels.map(formatTimestamp);

    // Get colors from the selected color scheme
    const selectedColors = COLOR_SCHEMES[chartConfig.colorScheme || colorScheme] || COLOR_SCHEMES.mixed;

    // Parse data keys
    const dataKeys = chartConfig.dataKey.split(',').map(key => key.trim());

    // Create datasets based on chart type
    let datasets = [];

    if (chartConfig.chartType === 'Pie') {
        // For pie charts, use aggregated data (latest values)
        const pieData = dataKeys.map(key => {
            const data = datapointsMap[key];
            return data ? data[data.length - 1] : 0; // Get latest value
        });

        datasets = [{
            label: chartConfig.label,
            data: pieData,
            backgroundColor: selectedColors.slice(0, dataKeys.length).map(color => hexToRgba(color, 0.8)),
            borderColor: selectedColors.slice(0, dataKeys.length).map(color => hexToRgba(color, 1)),
            borderWidth: 2
        }];
    } else {
        // For line, bar, area charts, create datasets for each metric
        datasets = dataKeys.map((key, index) => {
            const data = datapointsMap[key] || [];
            const color = selectedColors[index % selectedColors.length];
            const isAreaChart = chartConfig.chartType.toLowerCase() === 'area';

            // Create gradient for area charts
            let backgroundColor;
            if (isAreaChart) {
                // For area charts, we'll use a function that creates gradient when the chart is rendered
                backgroundColor = (context) => {
                    const chart = context.chart;
                    const { ctx, chartArea } = chart;

                    if (!chartArea) {
                        // This case happens on initial chart load
                        return null;
                    }

                    // Create linear gradient from top to bottom
                    const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
                    const baseColor = hexToRgba(color, 0.8);
                    const transparentColor = hexToRgba(color, 0.1);

                    gradient.addColorStop(0, baseColor);
                    gradient.addColorStop(1, transparentColor);

                    return gradient;
                };
            } else {
                backgroundColor = hexToRgba(color, 0.8);
            }

            return {
                label: key.replace(/_/g, ' ').toUpperCase(),
                data: data,
                backgroundColor: backgroundColor,
                borderColor: hexToRgba(color, 1),
                borderWidth: isAreaChart ? 3 : 2, // Slightly thicker border for area charts
                fill: isAreaChart,
                tension: (chartConfig.chartType.toLowerCase() === 'line' || isAreaChart) ? 0.4 : 0.1,
                pointBackgroundColor: hexToRgba(color, 1),
                pointBorderColor: '#ffffff',
                pointBorderWidth: isAreaChart ? 2 : 1,
                pointRadius: isAreaChart ? 4 : 3,
                pointHoverRadius: isAreaChart ? 6 : 5
            };
        });
    }

    // Handle table type separately
    if (chartConfig.chartType.toLowerCase() === 'table') {
        return await generateTableImage(chartConfig, chartData);
    }

    // Map unsupported chart types to supported ones
    let chartType = chartConfig.chartType.toLowerCase();
    if (chartType === 'count' || chartType === 'meter') {
        chartType = 'bar'; // Default unsupported types to bar charts
    }
    if (chartType === 'bar-h') {
        chartType = 'bar'; // Horizontal bar charts use regular bar type with indexAxis: 'y'
    }
    if (chartType === 'area' || chartType === 'range') {
        chartType = 'line'; // Area charts are line charts with fill: true
    }

    // Configure chart based on type
    const configuration = {
        type: chartType,
        data: {
            labels: chartConfig.chartType === 'Pie' ? dataKeys.map(key => key.replace(/_/g, ' ').toUpperCase()) : formattedLabels,
            datasets: datasets
        },
        options: {
            responsive: false,
            indexAxis: chartConfig.chartType === 'Bar-H' ? 'y' : 'x', // Horizontal bars
            // Enhanced settings for area charts
            ...(chartConfig.chartType.toLowerCase() === 'area' && {
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    point: {
                        hoverRadius: 8,
                        hoverBorderWidth: 3
                    }
                }
            }),
            plugins: {
                title: {
                    display: true,
                    text: `${chartConfig.label} - ${chartConfig.chartType.toUpperCase()} CHART`,
                    font: {
                        size: 20,  // Increased from 18
                        weight: 'bold',
                        family: 'Arial, sans-serif'
                    },
                    padding: {
                        top: 15,
                        bottom: 25
                    }
                },
                legend: {
                    display: true,
                    position: chartConfig.chartType === 'Pie' ? 'right' : 'top',
                    labels: {
                        font: {
                            size: 12,
                            family: 'Arial, sans-serif'
                        },
                        padding: 15
                    }
                }
            },
            scales: chartConfig.chartType === 'Pie' ? {} : {
                y: {
                    beginAtZero: chartConfig.chartType.toLowerCase() === 'area' ? true : false, // Area charts look better starting from zero
                    stacked: chartConfig.stacked || false,
                    title: {
                        display: true,
                        text: 'Value',
                        font: {
                            size: 14,
                            family: 'Arial, sans-serif'
                        }
                    },
                    ticks: {
                        font: {
                            size: 11,
                            family: 'Arial, sans-serif'
                        },
                        callback: function (value) {
                            return value.toFixed(chartConfig.decimal || 2);
                        }
                    }
                },
                x: {
                    stacked: chartConfig.stacked || false,
                    title: {
                        display: true,
                        text: 'Date',
                        font: {
                            size: 14,
                            family: 'Arial, sans-serif'
                        }
                    },
                    ticks: {
                        font: {
                            size: 11,
                            family: 'Arial, sans-serif'
                        }
                    }
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            }
        }
    };

    return await chartJSNodeCanvas.renderToBuffer(configuration);
}

/**
 * Fetch chart data from API
 * @param {string} url - API URL
 * @param {string} token - Authorization token
 * @returns {Promise<Object>} - Chart data
 */
async function fetchChartData(url, token) {
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error fetching chart data:', error);
        throw error;
    }
}

/**
 * Generate multi-chart PDF report based on dashboard configuration
 * @param {Array} dashboardList - Array of dashboard configurations
 * @param {string} token - Authorization token
 * @param {string} outputPath - Path where the PDF will be saved
 * @param {Object} options - Additional options (from, to dates, etc.)
 * @returns {Promise<string>} - Returns the path of the generated PDF
 */
async function generateMultiPdfReport(dashboardList, token, outputPath = './output/multi-chart-report.pdf', options = {}) {
    try {
        console.log('Starting multi-chart PDF report generation...');

        // Ensure output directory exists
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // Default date range (can be overridden by options)
        const from = options.from || '2025-05-31T18:30:00.000Z';
        const to = options.to || '2025-07-15T18:29:59.999Z';
        const baseUrl = 'https://services-dev.oneiot.io/anl/api/stats/ts/points';

        // Load logo
        const logoData = loadImage('./assets/logo.png')
        const topImage = loadImage('./assets/report-top.png');
        const bottomImage = loadImage('./assets/report-bottom.png');
        const createdAt = new Date().toLocaleString();

        // Create PDF document
        const pdf = new jsPDF('p', 'mm', 'a4'); // Portrait orientation for better chart display
        const pageWidth = pdf.internal.pageSize.getWidth();

        // Create first page with logo and title
        createFirstPage(pdf, logoData, topImage, bottomImage);

        // Add second page with report summary
        pdf.addPage();
        createReportSummaryPage(pdf, logoData, dashboardList, from, to, createdAt);

        // Generate charts for each dashboard configuration
        for (let i = 0; i < dashboardList.length; i++) {
            const config = dashboardList[i];
            console.log(`Generating chart ${i + 1}/${dashboardList.length}: ${config.label} (${config.chartType})`);

            try {
                // Build dynamic API URL
                const apiUrl = buildApiUrl(config, baseUrl, from, to);
                console.log(`Fetching data from: ${apiUrl}`);

                // Fetch chart data
                const chartData = await fetchChartData(apiUrl, token);
                console.log(`Chart data fetched for ${config.label}:`, chartData ? 'Success' : 'Failed');

                // Generate chart based on type
                let chartBuffer;
                if (config.chartType.toLowerCase() === 'table') {
                    chartBuffer = await generateTableImage(config, chartData);
                } else {
                    chartBuffer = await generateDynamicChart(config, chartData, config.colorScheme || 'mixed');
                }

                // Add new page for each chart
                pdf.addPage();
                const currentPageNumber = 3 + i; // Page 1 = title, Page 2 = summary, Page 3+ = charts

                // Add header
                addHeader(pdf, logoData);

                // Add chart title
                pdf.setFontSize(18);
                pdf.setFont('helvetica', 'bold');
                pdf.setTextColor(44, 62, 80);
                pdf.text(`${config.label}`, 20, 55);

                pdf.setFontSize(12);
                pdf.setFont('helvetica', 'normal');
                pdf.setTextColor(127, 140, 141);
                pdf.text(`${config.chartType} Chart | Chart ${i + 1} of ${dashboardList.length}`, 20, 70);

                // Convert buffer to base64 and add to PDF with improved quality
                const chartBase64 = chartBuffer.toString('base64');

                // Calculate optimal dimensions maintaining aspect ratio
                const chartAspectRatio = width / height; // 1200/600 = 2:1
                const maxWidth = pageWidth - 20; // Leave 10mm margin on each side
                const pageHeight = pdf.internal.pageSize.getHeight();
                const maxHeight = Math.min(120, pageHeight - 200); // Leave space for header, footer, and details

                let imageWidth = maxWidth;
                let imageHeight = imageWidth / chartAspectRatio;

                // If height exceeds max, scale down
                if (imageHeight > maxHeight) {
                    imageHeight = maxHeight;
                    imageWidth = imageHeight * chartAspectRatio;
                }

                // Center the image horizontally
                const xPosition = (pageWidth - imageWidth) / 2;

                const chartYPosition = 80; // Chart Y position

                // Add image with optimal dimensions and positioning (adjusted for header)
                pdf.addImage(
                    `data:image/png;base64,${chartBase64}`,
                    'PNG',
                    xPosition,
                    chartYPosition,
                    imageWidth,
                    imageHeight,
                    undefined, // alias
                    'FAST' // compression - use 'FAST' for better quality
                );

                // Calculate position for configuration details below the chart
                const footerSpace = 40; // Space reserved for footer
                const detailsStartY = chartYPosition + imageHeight + 15; // 15mm gap below chart
                const availableSpace = pageHeight - footerSpace - detailsStartY;

                // Only add configuration details if there's enough space
                if (availableSpace > 50) { // Need at least 50mm for details
                    pdf.setFontSize(8);
                    pdf.setTextColor(44, 62, 80);

                    let detailsY = detailsStartY;
                    pdf.text('Configuration Details:', 20, detailsY);
                    pdf.text(`• Chart ID: ${config.id}`, 25, detailsY + 8);
                    pdf.text(`• Stacked: ${config.stacked ? 'Yes' : 'No'}`, 25, detailsY + 16);
                    pdf.text(`• Expanded: ${config.expanded ? 'Yes' : 'No'}`, 25, detailsY + 24);
                    pdf.text(`• Color Scheme: ${config.colorScheme || 'mixed'}`, 25, detailsY + 32);

                    // Add filters information if there's space
                    if (config.filters && config.filters.length > 0 && availableSpace > 80) {
                        pdf.text('Filters Applied:', 25, detailsY + 40);
                        config.filters.forEach((filter, filterIndex) => {
                            if (detailsY + 48 + (filterIndex * 8) < pageHeight - footerSpace) {
                                pdf.text(`  ${filterIndex + 1}. ${filter.field} ${filter.operation} ${filter.value.join(', ')}`, 30, detailsY + 48 + (filterIndex * 8));
                            }
                        });
                    }
                }

                // Add footer
                addFooter(pdf, currentPageNumber, createdAt);

            } catch (error) {
                console.error(`Error generating chart for ${config.label}:`, error);

                // Add error page
                pdf.addPage();
                const errorPageNumber = 3 + i;

                // Add header
                addHeader(pdf, logoData);

                // Add error content
                pdf.setFontSize(18);
                pdf.setFont('helvetica', 'bold');
                pdf.setTextColor(231, 76, 60); // Red color for error
                pdf.text(`Error: ${config.label}`, 20, 55);

                pdf.setFontSize(12);
                pdf.setFont('helvetica', 'normal');
                pdf.setTextColor(44, 62, 80);
                pdf.text(`Failed to generate ${config.chartType} chart`, 20, 75);
                pdf.text(`Error: ${error.message}`, 20, 90);

                // Add footer
                addFooter(pdf, errorPageNumber, createdAt);
            }
        }

        // Save PDF
        pdf.save(outputPath);

        console.log(`Multi-chart PDF report generated successfully: ${outputPath}`);
        return outputPath;

    } catch (error) {
        console.error('Error generating multi-chart PDF report:', error);
        throw error;
    }
}

module.exports = {
    generateDynamicChart,
    buildApiUrl,
    fetchChartData,
    generateMultiPdfReport,
    COLOR_SCHEMES
};
