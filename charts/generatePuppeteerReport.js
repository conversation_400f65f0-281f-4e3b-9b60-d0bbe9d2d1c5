const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

/**
 * Load logo image and convert to base64
 * @returns {Object|null} - Object with base64 data and dimensions, or null if not found
 */
function loadLogo() {
    try {
        const logoPath = path.resolve('./assets/logo.png');
        if (fs.existsSync(logoPath)) {
            const logoBuffer = fs.readFileSync(logoPath);
            const base64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;

            // Get image dimensions using a simple PNG header parser
            let width = 100, height = 100; // Default fallback
            try {
                // PNG files have width and height at bytes 16-23
                if (logoBuffer.length > 24 && logoBuffer.toString('ascii', 1, 4) === 'PNG') {
                    width = logoBuffer.readUInt32BE(16);
                    height = logoBuffer.readUInt32BE(20);
                }
            } catch (e) {
                console.warn('Could not read PNG dimensions, using defaults');
            }

            return { base64, width, height };
        } else {
            console.warn('Logo file not found at:', logoPath);
            return null;
        }
    } catch (error) {
        console.error('Error loading logo:', error);
        return null;
    }
}

/**
 * Format timestamp for display
 * @param {string} timestamp - ISO timestamp string
 * @returns {string} - Formatted timestamp
 */
function formatTimestamp(timestamp) {
    try {
        const date = new Date(timestamp);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return timestamp;
    }
}

/**
 * Convert hex color to rgba
 * @param {string} hex - Hex color string
 * @param {number} alpha - Alpha value (0-1)
 * @returns {string} - RGBA color string
 */
function hexToRgba(hex, alpha = 1) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

/**
 * Get color scheme colors
 * @param {string} colorScheme - Color scheme name
 * @returns {Array} - Array of hex colors
 */
function getColorScheme(colorScheme) {
    const colorSchemes = {
        default: ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'],
        ruby: ['#e74c3c', '#c0392b', '#a93226', '#922b21', '#7b241c', '#641e16'],
        emerald: ['#2ecc71', '#27ae60', '#229954', '#1e8449', '#196f3d', '#145a32'],
        mixed: ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c']
    };
    return colorSchemes[colorScheme] || colorSchemes.default;
}

/**
 * Fetch chart data from API
 * @param {string} url - API URL
 * @param {string} token - Authorization token
 * @returns {Promise<Object>} - Chart data
 */
async function fetchChartData(url, token) {
    try {
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching chart data:', error.message);
        throw new Error(`API request failed: ${error.response?.status || 'Unknown'}`);
    }
}

/**
 * Build dynamic API URL based on dashboard configuration
 * @param {Object} dashConfig - Dashboard configuration object
 * @param {string} baseUrl - Base API URL
 * @param {string} from - Start date
 * @param {string} to - End date
 * @returns {string} - Complete API URL
 */
function buildApiUrl(dashConfig, baseUrl, from, to) {
    // Use different endpoint for table charts
    if (dashConfig.chartType.toLowerCase() === 'table') {
        const tableBaseUrl = 'https://services-dev.oneiot.io/anl/api/stats/ts/table';
        const params = new URLSearchParams();

        // Add filters as JSON string
        if (dashConfig.filters && dashConfig.filters.length > 0) {
            params.append('filters', JSON.stringify(dashConfig.filters));
        }

        // Add date range
        params.append('from', from);
        params.append('to', to);

        return `${tableBaseUrl}?${params.toString()}`;
    }

    // For non-table charts, use the original logic
    const params = new URLSearchParams();

    // Add product name from filters
    const productFilter = dashConfig.filters?.find(f => f.field === 'productName');
    if (productFilter && productFilter.value?.length > 0) {
        params.append('productName', productFilter.value[0]);
    }

    // Add date range
    params.append('from', from);
    params.append('to', to);

    // Add anomalies flag
    params.append('anomalies', 'true');

    // Add data keys
    if (dashConfig.dataKey) {
        params.append('key', dashConfig.dataKey);
    }

    return `${baseUrl}?${params.toString()}`;
}

/**
 * Generate HTML content for the PDF report
 * @param {Array} dashboardList - Array of dashboard configurations
 * @param {string} from - Start date
 * @param {string} to - End date
 * @param {Object} logoData - Logo data with base64 and dimensions
 * @param {Array} chartImages - Array of chart image data
 * @returns {string} - Complete HTML content
 */
function generateReportHTML(dashboardList, from, to, logoData, chartImages) {
    const createdAt = new Date().toLocaleString();

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Dynamic Scheduled Reports</title>
        <style>
            @page {
                size: A4;
                margin: 15mm;
            }
            
            body {
                font-family: 'Arial', sans-serif;
                margin: 0;
                padding: 0;
                color: #2c3e50;
                line-height: 1.6;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .header {
                display: flex;
                align-items: center;
                padding-bottom: 10px;
                border-bottom: 2px solid #34495e;
                margin-bottom: 20px;
            }
            
            .header img {
                height: 40px;
                margin-right: 15px;
            }
            
            .footer {
                position: fixed;
                bottom: 10mm;
                right: 15mm;
                font-size: 10px;
                color: #7f8c8d;
                border-top: 1px solid #34495e;
                padding-top: 5px;
            }
            
            .title-page {
                text-align: center;
                height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            
            .title-page img {
                max-width: 200px;
                max-height: 150px;
                margin-bottom: 30px;
            }
            
            .title-page h1 {
                font-size: 48px;
                color: #2c3e50;
                margin: 20px 0;
                font-weight: bold;
            }
            
            .title-page h2 {
                font-size: 24px;
                color: #7f8c8d;
                margin: 10px 0;
                font-weight: normal;
            }
            
            .title-page .date {
                font-size: 16px;
                color: #95a5a6;
                margin-top: 50px;
            }
            
            .summary-page h1 {
                color: #2c3e50;
                font-size: 36px;
                margin-bottom: 30px;
            }
            
            .summary-section {
                margin-bottom: 30px;
            }
            
            .summary-section h2 {
                color: #34495e;
                font-size: 20px;
                margin-bottom: 15px;
                border-bottom: 1px solid #bdc3c7;
                padding-bottom: 5px;
            }
            
            .summary-list {
                list-style: none;
                padding: 0;
            }
            
            .summary-list li {
                margin: 8px 0;
                padding-left: 20px;
                position: relative;
            }
            
            .summary-list li:before {
                content: "•";
                color: #3498db;
                font-weight: bold;
                position: absolute;
                left: 0;
            }
            
            .chart-page {
                margin-bottom: 40px;
            }
            
            .chart-title {
                font-size: 24px;
                color: #2c3e50;
                margin-bottom: 10px;
                font-weight: bold;
            }
            
            .chart-subtitle {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 20px;
            }
            
            .chart-image {
                width: 100%;
                max-width: 100%;
                height: auto;
                margin: 20px 0;
                border: 1px solid #ecf0f1;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .chart-details {
                font-size: 10px;
                color: #7f8c8d;
                margin-top: 15px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
            
            .chart-details h4 {
                margin: 0 0 8px 0;
                color: #2c3e50;
            }
            
            .error-page {
                text-align: center;
                padding: 50px 20px;
            }
            
            .error-title {
                color: #e74c3c;
                font-size: 24px;
                margin-bottom: 20px;
            }
            
            .error-message {
                color: #2c3e50;
                font-size: 16px;
            }
        </style>
    </head>
    <body>
        <!-- Title Page -->
        <div class="title-page">
            ${logoData ? `<img src="${logoData.base64}" alt="Logo">` : ''}
            <h1>Dynamic Scheduled Reports</h1>
            <h2>Automated IoT Analytics Dashboard</h2>
            <div class="date">Generated on: ${createdAt}</div>
        </div>
        
        <!-- Summary Page -->
        <div class="page-break">
            <div class="header">
                ${logoData ? `<img src="${logoData.base64}" alt="Logo">` : ''}
            </div>
            
            <div class="summary-page">
                <h1>Report Summary</h1>
                
                <div class="summary-section">
                    <h2>Report Information</h2>
                    <ul class="summary-list">
                        <li>Data Period: ${new Date(from).toLocaleDateString()} - ${new Date(to).toLocaleDateString()}</li>
                        <li>Total Charts: ${dashboardList.length}</li>
                        <li>Generated: ${createdAt}</li>
                    </ul>
                </div>
                
                <div class="summary-section">
                    <h2>Dashboard Components</h2>
                    <ul class="summary-list">
                        ${Object.entries(dashboardList.reduce((acc, config) => {
        const type = config.chartType;
        if (!acc[type]) acc[type] = [];
        acc[type].push(config);
        return acc;
    }, {})).map(([type, charts]) => `
                            <li>${type} Charts: ${charts.length}</li>
                        `).join('')}
                    </ul>
                </div>
            </div>
            
            <div class="footer">
                Page 2 | Generated: ${createdAt}
            </div>
        </div>
        
        ${chartImages.map((chartData, index) => `
            <!-- Chart Page ${index + 1} -->
            <div class="page-break">
                <div class="header">
                    ${logoData ? `<img src="${logoData.base64}" alt="Logo">` : ''}
                </div>
                
                <div class="chart-page">
                    <div class="chart-title">${chartData.config.label}</div>
                    <div class="chart-subtitle">${chartData.config.chartType} Chart | Chart ${index + 1} of ${dashboardList.length}</div>
                    
                    ${chartData.error ? `
                        <div class="error-page">
                            <div class="error-title">Error: ${chartData.config.label}</div>
                            <div class="error-message">Failed to generate ${chartData.config.chartType} chart<br>Error: ${chartData.error}</div>
                        </div>
                    ` : `
                        <img src="${chartData.imageBase64}" alt="${chartData.config.label}" class="chart-image">
                        
                        <div class="chart-details">
                            <h4>Configuration Details:</h4>
                            • Chart ID: ${chartData.config.id}<br>
                            • Stacked: ${chartData.config.stacked ? 'Yes' : 'No'}<br>
                            • Expanded: ${chartData.config.expanded ? 'Yes' : 'No'}<br>
                            • Color Scheme: ${chartData.config.colorScheme || 'mixed'}<br>
                            ${chartData.config.filters && chartData.config.filters.length > 0 ? `
                                <br><strong>Filters Applied:</strong><br>
                                ${chartData.config.filters.map((filter, filterIndex) =>
        `${filterIndex + 1}. ${filter.field} ${filter.operation} ${filter.value.join(', ')}`
    ).join('<br>')}
                            ` : ''}
                        </div>
                    `}
                </div>
                
                <div class="footer">
                    Page ${index + 3} | Generated: ${createdAt}
                </div>
            </div>
        `).join('')}
    </body>
    </html>
    `;
}

/**
 * Generate chart image using Chart.js in browser context
 * @param {Object} chartConfig - Chart configuration
 * @param {Object} chartData - Chart data from API
 * @param {string} colorScheme - Color scheme to use
 * @returns {Promise<string>} - Base64 encoded chart image
 */
async function generateChartImage(chartConfig, chartData, colorScheme = 'mixed') {
    // Handle table type separately
    if (chartConfig.chartType.toLowerCase() === 'table') {
        return await generateTableImageHTML(chartConfig, chartData);
    }

    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();

    try {
        // Set viewport for consistent chart rendering
        await page.setViewport({ width: 1200, height: 600 });

        // Create HTML with Chart.js
        const chartHTML = await generateChartHTML(chartConfig, chartData, colorScheme);
        await page.setContent(chartHTML);

        // Wait for chart to render
        await page.waitForSelector('#chartCanvas');

        // Wait for chart to be fully rendered (alternative to waitForTimeout)
        await page.evaluate(() => {
            return new Promise((resolve) => {
                setTimeout(resolve, 2000); // Give time for chart animation
            });
        });

        // Take screenshot of the chart
        const chartElement = await page.$('#chartCanvas');
        const imageBuffer = await chartElement.screenshot({ type: 'png' });

        await browser.close();
        return `data:image/png;base64,${imageBuffer.toString('base64')}`;

    } catch (error) {
        await browser.close();
        throw error;
    }
}

/**
 * Generate HTML for chart rendering
 * @param {Object} chartConfig - Chart configuration
 * @param {Object} chartData - Chart data from API
 * @param {string} colorScheme - Color scheme to use
 * @returns {string} - HTML content for chart
 */
async function generateChartHTML(chartConfig, chartData, colorScheme) {
    const { labels, datapointsMap } = chartData.data;
    const formattedLabels = labels.map(formatTimestamp);
    const dataKeys = chartConfig.dataKey ? chartConfig.dataKey.split(',').map(key => key.trim()) : [];
    const selectedColors = getColorScheme(colorScheme);

    // Create datasets based on chart type
    let datasets;
    if (chartConfig.chartType === 'Pie') {
        const pieData = dataKeys.map(key => {
            const data = datapointsMap[key] || [];
            return data.length > 0 ? data[data.length - 1] : 0;
        });

        datasets = [{
            label: 'Data',
            data: pieData,
            backgroundColor: selectedColors.slice(0, dataKeys.length).map(color => hexToRgba(color, 0.8)),
            borderColor: selectedColors.slice(0, dataKeys.length).map(color => hexToRgba(color, 1)),
            borderWidth: 2
        }];
    } else {
        datasets = dataKeys.map((key, index) => {
            const data = datapointsMap[key] || [];
            const color = selectedColors[index % selectedColors.length];
            const isAreaChart = chartConfig.chartType.toLowerCase() === 'area';

            return {
                label: key.replace(/_/g, ' ').toUpperCase(),
                data: data,
                backgroundColor: isAreaChart ? hexToRgba(color, 0.3) : hexToRgba(color, 0.8),
                borderColor: hexToRgba(color, 1),
                borderWidth: isAreaChart ? 3 : 2,
                fill: isAreaChart,
                tension: (chartConfig.chartType.toLowerCase() === 'line' || isAreaChart) ? 0.4 : 0.1,
                pointBackgroundColor: hexToRgba(color, 1),
                pointBorderColor: '#ffffff',
                pointBorderWidth: isAreaChart ? 2 : 1,
                pointRadius: isAreaChart ? 4 : 3
            };
        });
    }

    // Map chart types
    let chartType = chartConfig.chartType.toLowerCase();
    if (chartType === 'count' || chartType === 'meter') {
        chartType = 'bar';
    }
    if (chartType === 'bar-h') {
        chartType = 'bar';
    }
    if (chartType === 'area' || chartType === 'range') {
        chartType = 'line';
    }

    const chartConfiguration = {
        type: chartType,
        data: {
            labels: chartConfig.chartType === 'Pie' ? dataKeys.map(key => key.replace(/_/g, ' ').toUpperCase()) : formattedLabels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: chartConfig.chartType === 'Bar-H' ? 'y' : 'x',
            plugins: {
                title: {
                    display: true,
                    text: `${chartConfig.label} - ${chartConfig.chartType.toUpperCase()} CHART`,
                    font: { size: 20, weight: 'bold', family: 'Arial, sans-serif' },
                    padding: { top: 15, bottom: 25 }
                },
                legend: {
                    display: true,
                    position: chartConfig.chartType === 'Pie' ? 'right' : 'top',
                    labels: { font: { size: 12, family: 'Arial, sans-serif' }, padding: 15 }
                }
            },
            scales: chartConfig.chartType === 'Pie' ? {} : {
                y: {
                    beginAtZero: chartConfig.chartType.toLowerCase() === 'area' ? true : false,
                    stacked: chartConfig.stacked || false,
                    title: { display: true, text: 'Value', font: { size: 14, family: 'Arial, sans-serif' } },
                    ticks: {
                        font: { size: 11, family: 'Arial, sans-serif' },
                        callback: function (value) { return value.toFixed(chartConfig.decimal || 2); }
                    }
                },
                x: {
                    stacked: chartConfig.stacked || false,
                    title: { display: true, text: 'Time', font: { size: 14, family: 'Arial, sans-serif' } },
                    ticks: { font: { size: 11, family: 'Arial, sans-serif' } }
                }
            }
        }
    };

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body { margin: 0; padding: 20px; background: white; }
            #chartContainer { width: 1160px; height: 560px; }
        </style>
    </head>
    <body>
        <div id="chartContainer">
            <canvas id="chartCanvas"></canvas>
        </div>
        <script>
            const ctx = document.getElementById('chartCanvas').getContext('2d');
            const config = ${JSON.stringify(chartConfiguration)};

            // Add gradient for area charts
            if (config.type === 'line') {
                config.data.datasets.forEach((dataset, index) => {
                    if (dataset.fill) {
                        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
                        const color = dataset.borderColor;
                        gradient.addColorStop(0, color.replace('1)', '0.6)'));
                        gradient.addColorStop(1, color.replace('1)', '0.1)'));
                        dataset.backgroundColor = gradient;
                    }
                });
            }

            new Chart(ctx, config);
        </script>
    </body>
    </html>
    `;
}

/**
 * Generate table image as HTML
 * @param {Object} chartConfig - Chart configuration
 * @param {Object} chartData - Chart data from API
 * @returns {Promise<string>} - Base64 encoded table image
 */
async function generateTableImageHTML(chartConfig, chartData) {
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();

    try {
        await page.setViewport({ width: 1200, height: 800 });

        // Process table data
        let tableData, columns;

        if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
            // Table API response format
            const rawData = chartData.data.data;

            // Flatten the data structure
            tableData = rawData.map(item => {
                const flattened = {
                    thingName: item.thingName,
                    productName: item.productName,
                    timestamp: item.timestamp,
                    uniqueId: item.uniqueId
                };

                if (item.payload) {
                    Object.keys(item.payload).forEach(key => {
                        flattened[key] = item.payload[key];
                    });
                }

                return flattened;
            });

            // Prioritize important columns
            const allColumns = new Set();
            tableData.forEach(row => {
                Object.keys(row).forEach(key => allColumns.add(key));
            });

            const priorityColumns = ['thingName', 'timestamp', 'speed', 'body_angle', 'engine_rpm', 'fuel_level'];
            const otherColumns = Array.from(allColumns).filter(col => !priorityColumns.includes(col));
            columns = [...priorityColumns.filter(col => allColumns.has(col)), ...otherColumns.slice(0, 6)].slice(0, 10);
        } else {
            tableData = [];
            columns = [];
        }

        const tableHTML = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: white; }
                .table-container { width: 100%; overflow-x: auto; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th { background-color: #4CAF50; color: white; padding: 12px 8px; text-align: center; font-weight: bold; }
                td { padding: 8px; text-align: center; border: 1px solid #ddd; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                tr:hover { background-color: #f5f5f5; }
                .title { text-align: center; font-size: 24px; font-weight: bold; color: #333; margin-bottom: 20px; }
                .no-data { text-align: center; padding: 40px; color: #666; font-size: 16px; }
            </style>
        </head>
        <body>
            <div class="title">${chartConfig.label} - TABLE</div>
            ${tableData.length === 0 ? `
                <div class="no-data">No data available for this table</div>
            ` : `
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                ${columns.map(column => {
            let displayName;
            switch (column) {
                case 'thingName': displayName = 'TRUCK'; break;
                case 'timestamp': displayName = 'TIME'; break;
                case 'speed': displayName = 'SPEED'; break;
                case 'body_angle': displayName = 'BODY ANGLE'; break;
                case 'engine_rpm': displayName = 'RPM'; break;
                case 'fuel_level': displayName = 'FUEL'; break;
                default: displayName = column.replace(/_/g, ' ').toUpperCase();
            }
            return `<th>${displayName}</th>`;
        }).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${tableData.slice(0, 20).map(row => `
                                <tr>
                                    ${columns.map(column => {
            const value = row[column];
            let displayValue;

            if (column === 'timestamp' && value) {
                displayValue = formatTimestamp(value);
            } else if (column === 'thingName' && value) {
                displayValue = value.replace('haultruck_', 'T');
            } else if (typeof value === 'string' && !isNaN(parseFloat(value))) {
                displayValue = parseFloat(value).toFixed(chartConfig.decimal || 1);
            } else if (typeof value === 'number') {
                displayValue = value.toFixed(chartConfig.decimal || 1);
            } else {
                displayValue = value || 'N/A';
            }

            return `<td>${displayValue}</td>`;
        }).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                <div style="text-align: center; color: #666; font-size: 12px; margin-top: 15px;">
                    Showing ${Math.min(tableData.length, 20)} of ${tableData.length} records | Columns: ${columns.length}
                </div>
            `}
        </body>
        </html>
        `;

        await page.setContent(tableHTML);

        // Wait for table to render
        await page.evaluate(() => {
            return new Promise((resolve) => {
                setTimeout(resolve, 1000);
            });
        });

        const imageBuffer = await page.screenshot({ type: 'png', fullPage: true });
        await browser.close();

        return `data:image/png;base64,${imageBuffer.toString('base64')}`;

    } catch (error) {
        await browser.close();
        throw error;
    }
}

/**
 * Generate multi-chart PDF report using Puppeteer
 * @param {Array} dashboardList - Array of dashboard configurations
 * @param {string} outputPath - Output file path for the PDF
 * @param {Object} options - Options including date range and token
 * @returns {Promise<string>} - Path to generated PDF file
 */
async function generatePuppeteerReport(dashboardList, outputPath, options = {}) {
    try {
        console.log('Starting Puppeteer PDF report generation...');

        // Extract options
        const token = options.token || process.env.API_TOKEN || 'default-token';

        // Ensure output directory exists
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // Default date range
        const from = options.from || '2025-05-31T18:30:00.000Z';
        const to = options.to || '2025-07-15T18:29:59.999Z';
        const baseUrl = 'https://services-dev.oneiot.io/anl/api/stats/ts/points';

        // Load logo
        const logoData = loadLogo();

        // Generate chart images
        const chartImages = [];

        for (let i = 0; i < dashboardList.length; i++) {
            const config = dashboardList[i];
            console.log(`Generating chart ${i + 1}/${dashboardList.length}: ${config.label} (${config.chartType})`);

            try {
                // Build dynamic API URL
                const apiUrl = buildApiUrl(config, baseUrl, from, to);
                console.log(`Fetching data from: ${apiUrl}`);

                // Fetch chart data
                const chartData = await fetchChartData(apiUrl, token);
                console.log(`Chart data fetched for ${config.label}:`, chartData ? 'Success' : 'Failed');

                // Generate chart image
                const imageBase64 = await generateChartImage(config, chartData, config.colorScheme || 'mixed');

                chartImages.push({
                    config: config,
                    imageBase64: imageBase64,
                    error: null
                });

            } catch (error) {
                console.error(`Error generating chart for ${config.label}:`, error);

                chartImages.push({
                    config: config,
                    imageBase64: null,
                    error: error.message
                });
            }
        }

        // Generate complete HTML report
        const reportHTML = generateReportHTML(dashboardList, from, to, logoData, chartImages);

        // Generate PDF using Puppeteer
        const browser = await puppeteer.launch({ headless: true });
        const page = await browser.newPage();

        try {
            await page.setContent(reportHTML, { waitUntil: 'networkidle0' });

            // Generate PDF with proper settings
            await page.pdf({
                path: outputPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '15mm',
                    right: '15mm',
                    bottom: '15mm',
                    left: '15mm'
                }
            });

            await browser.close();

            console.log(`Puppeteer PDF report generated successfully: ${outputPath}`);
            return outputPath;

        } catch (error) {
            await browser.close();
            throw error;
        }

    } catch (error) {
        console.error('Error generating Puppeteer PDF report:', error);
        throw error;
    }
}

module.exports = {
    generatePuppeteerReport,
    generateChartImage,
    generateTableImageHTML,
    loadLogo,
    formatTimestamp,
    hexToRgba,
    getColorScheme,
    fetchChartData,
    buildApiUrl
};
